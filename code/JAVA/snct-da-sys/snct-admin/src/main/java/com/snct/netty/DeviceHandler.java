package com.snct.netty;

import io.netty.channel.ChannelInboundHandlerAdapter;

import java.util.List;

/**
 * 设备消息处理器接口
 * 所有具体设备的消息处理器都应实现此接口
 */
public abstract class DeviceHandler extends ChannelInboundHandlerAdapter {

    /**
     * 获取设备类型标识
     * 
     * @return 设备类型标识
     */
    public abstract String getDeviceType();
    
    /**
     * 发送查询命令
     * 定义设备查询命令的发送方法
     * 
     * @return 是否成功发送查询命令
     */
    public abstract boolean sendQueryCommand(List<String> commands);
} 