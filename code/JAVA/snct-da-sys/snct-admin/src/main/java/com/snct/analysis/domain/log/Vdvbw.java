package com.snct.analysis.domain.log;

import com.snct.system.domain.msg.Instrument;

/**
 * @description: Log的$VDVBW数据
 * Example:$VDVBW,0.1,,A,,,V,,V,,V*69
 * $VDVBW,<1>,,A,,,V,,V,,V*CS<CR><LF>
 * 逗号隔开
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Vdvbw extends Instrument {
    /**
     * 1.对水速度
     */
    private String waterSpeed;
    /**
     * 2.状态位 A=数据有效
     */
    private String status;


    @Override
    public void dataAnalysis(String dataStr) {
        String[] values = dataStr.split(",", -1);
        values = super.valuesTrim(values);
        waterSpeed = values[1];
        status = values[3];
    }

    public String getWaterSpeed() {
        return waterSpeed;
    }

    public void setWaterSpeed(String waterSpeed) {
        this.waterSpeed = waterSpeed;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
