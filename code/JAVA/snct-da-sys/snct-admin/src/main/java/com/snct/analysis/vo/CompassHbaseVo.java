package com.snct.analysis.vo;


import com.snct.common.annotation.Excel;

/**
 * @description:  罗经数据 1秒一组
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class CompassHbaseVo {

    private String id;
    /**
     * 录入时间
     */
    private String initialTime;
    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    private String initialBjTime;

    /**
     * 当前罗经数据
     */
    @Excel(name="当前罗经数据")
    private String hehdt;

    /**
     * 转向速率  Rate Of Turn负数表示向左舷转
     */
    @Excel(name="转向速率")
    private String herot;



    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getHehdt() {
        return hehdt;
    }

    public void setHehdt(String hehdt) {
        this.hehdt = hehdt;
    }

    public String getHerot() {
        return herot;
    }

    public void setHerot(String herot) {
        this.herot = herot;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }


}
