package com.snct.analysis.domain;

import com.snct.system.domain.Device;
import io.netty.buffer.ByteBuf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AmProtocol {
    protected Logger logger;
    private int type;
    private String name;
    private String mac;
    private byte[] content;
    private Long time;
    private int contentLength;

    public int getContentLength() {
        return this.contentLength;
    }

    public AmProtocol(Device device, byte[] content) {
        this.logger = LoggerFactory.getLogger(AmProtocol.class);
        this.mac = device.getMac();
        this.type = device.getType().intValue();
        this.content = content;
        this.contentLength = content.length;
        this.time = Long.valueOf(System.currentTimeMillis());
    }

    public void setContentLength(int contentLength) {
        this.contentLength = contentLength;
    }

    //public AmProtocol(AcquisitionMiddleware am, byte[] content) {
    //    this.logger = LoggerFactory.getLogger(AmProtocol.class);
    //    this.mac = am.getMac();
    //    this.type = am.getType().intValue();
    //    this.content = content;
    //    this.contentLength = content.length;
    //    this.time = Long.valueOf(System.currentTimeMillis());
    //}

    //public AmProtocol(ByteBuf byteBuf, AcquisitionMiddleware am) {
    //    this.logger = LoggerFactory.getLogger(AmProtocol.class);
    //    byte[] allBytes = new byte[byteBuf.readableBytes()];
    //    byteBuf.readBytes(allBytes);
    //    String allStr = new String(allBytes, StandardCharsets.UTF_8);
    //    this.logger.info("原始数据：{}", allStr);
    //    if (allStr.contains("&&&")) {
    //        if (allStr.length() <= 48) {
    //            return;
    //        }
    //        int headLen = allStr.indexOf("@@");
    //        String headStr = allStr.substring(0, headLen + 2);
    //        String[] headArr = headStr.replace("&&&", "").replace("@@", "").split("#");
    //        this.name = headArr[1];
    //        this.mac = headArr[2];
    //        byteBuf.readerIndex(headLen + 2);
    //        this.content = new byte[byteBuf.readableBytes()];
    //        byteBuf.readBytes(this.content);
    //    } else {
    //        this.name = am.getName();
    //        this.mac = am.getMac();
    //        this.content = allBytes;
    //    }
    //    this.time = Long.valueOf(System.currentTimeMillis());
    //}

    public int getType() {
        return this.type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMac() {
        return this.mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public byte[] getContent() {
        return this.content;
    }

    public void setContent(byte[] content) {
        this.content = content;
    }

    public Long getTime() {
        return this.time;
    }

    public void setTime(Long time) {
        this.time = time;
    }
}