package com.snct.web.controller.datetime;

import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.utils.DateUtils;
import com.snct.common.utils.StringUtils;
import com.snct.utils.SysCmd;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统时间同步控制器
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/datetime")
public class SyncDateTimeController extends BaseController {

    @Autowired
    private SysCmd sysCmd;

    /**
     * 获取当前系统时间
     * @return 系统时间信息
     */
    @GetMapping("/current")
    public AjaxResult getCurrentTime() {
        try {
            // 获取系统时间
            String systemTime = sysCmd.getCurrentSystemTime();

            if (StringUtils.isEmpty(systemTime)) {
                return error("获取系统时间失败");
            }

            // 同时获取Java应用时间作为对比
            String javaTime = DateUtils.getTime();

            Map<String, Object> result = new HashMap<>();
            result.put("systemTime", systemTime.trim());
            result.put("javaTime", javaTime);
            result.put("timestamp", System.currentTimeMillis());

            return AjaxResult.success("获取系统时间成功", result);
        } catch (Exception e) {
            e.printStackTrace();
            return error("获取系统时间异常: " + e.getMessage());
        }
    }

    /**
     * 更新系统时间
     * @param dateTime 要设置的时间，格式：yyyy-MM-dd HH:mm:ss
     * @return 操作结果
     */
    @PostMapping("/update")
    public AjaxResult updateTime(@RequestParam("dateTime") String dateTime) {
        try {
            // 验证时间格式
            if (StringUtils.isEmpty(dateTime)) {
                return error("时间参数不能为空");
            }

            // 验证时间格式是否正确
            try {
                DateUtils.parseDateTime(dateTime);
            } catch (Exception e) {
                return error("时间格式不正确，请使用格式：yyyy-MM-dd HH:mm:ss");
            }

            // 设置系统时间
            boolean success = sysCmd.setSystemTime(dateTime);

            if (success) {
                // 获取设置后的系统时间进行确认
                String newSystemTime = sysCmd.getCurrentSystemTime();

                Map<String, Object> result = new HashMap<>();
                result.put("oldTime", DateUtils.getTime());
                result.put("newTime", newSystemTime != null ? newSystemTime.trim() : "获取失败");
                result.put("setTime", dateTime);

                return AjaxResult.success("系统时间更新成功", result);
            } else {
                return error("系统时间更新失败，请检查权限或时间格式");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return error("更新系统时间异常: " + e.getMessage());
        }
    }
}
