package com.snct.analysis.vo;

import com.snct.common.annotation.Excel;

/**
 * @description: 姿态仪数据
 * <AUTHOR>
 * @date 2025-06-20
 **/
public class AttitudeHbaseVo2 {

    /**
     * 录入时间
     */
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    private String initialBjTime;

    /**
     * 设备序列号
     */
    @Excel(name="设备序列号")
    private String serialNo;

    /**
     * UTC时间(时/分/秒/小数秒)
     */
    @Excel(name="UTC时间")
    private String utc;

    /**
     * 纬度
     */
    @Excel(name="纬度")
    private String lat;

    /**
     * 经度
     */
    @Excel(name="经度")
    private String lon;

    /**
     * 椭球高
     */
    @Excel(name="椭球高")
    private String elpHeight;

    /**
     * 航向角
     */
    @Excel(name="航向角")
    private String heading;

    /**
     * 俯仰角
     */
    @Excel(name="俯仰角")
    private String pitch;

    /**
     * 北方向速度
     */
    @Excel(name="北方向速度")
    private String velN;

    /**
     * 东方向速度
     */
    @Excel(name="东方向速度")
    private String velE;

    /**
     * 地向速度
     */
    @Excel(name="地向速度")
    private String velD;

    /**
     * 地面速度
     */
    @Excel(name="地面速度")
    private String velG;

    /**
     * 高精度坐标北向X轴参考PTNL和PK
     */
    @Excel(name="坐标北向")
    private String coordinateNorthing;

    /**
     * 高精度坐标东向Y轴参考PTNL和PK
     */
    @Excel(name="坐标东向")
    private String coordinateEasting;

    /**
     * 基站坐标下的移动站X坐标(基站坐标为原点)
     */
    @Excel(name="北距离")
    private String northDistance;

    /**
     * 基站坐标下的移动站Y坐标(基站坐标为原点)
     */
    @Excel(name="东距离")
    private String eastDistance;

    /**
     * 定位指示状态
     */
    @Excel(name="定位指示")
    private String positionIndicator;

    /**
     * 定向指示状态
     */
    @Excel(name="定向指示")
    private String headingIndicator;

    /**
     * 主导天线收星数
     */
    @Excel(name="收星数")
    private String svn;

    /**
     * 差分延迟
     */
    @Excel(name="差分延迟")
    private String diffAge;

    /**
     * 基准站ID
     */
    @Excel(name="基准站ID")
    private String stationId;

    /**
     * 主导和从站内之间的距离(双天线基线长)
     */
    @Excel(name="基线长度")
    private String baselineLength;

    /**
     * 从站参与解算的卫星数
     */
    @Excel(name="解算卫星数")
    private String solutionSv;

    /**
     * 横滚角
     */
    @Excel(name="横滚角")
    private String rolling;

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getUtc() {
        return utc;
    }

    public void setUtc(String utc) {
        this.utc = utc;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }

    public String getElpHeight() {
        return elpHeight;
    }

    public void setElpHeight(String elpHeight) {
        this.elpHeight = elpHeight;
    }

    public String getHeading() {
        return heading;
    }

    public void setHeading(String heading) {
        this.heading = heading;
    }

    public String getPitch() {
        return pitch;
    }

    public void setPitch(String pitch) {
        this.pitch = pitch;
    }

    public String getVelN() {
        return velN;
    }

    public void setVelN(String velN) {
        this.velN = velN;
    }

    public String getVelE() {
        return velE;
    }

    public void setVelE(String velE) {
        this.velE = velE;
    }

    public String getVelD() {
        return velD;
    }

    public void setVelD(String velD) {
        this.velD = velD;
    }

    public String getVelG() {
        return velG;
    }

    public void setVelG(String velG) {
        this.velG = velG;
    }

    public String getCoordinateNorthing() {
        return coordinateNorthing;
    }

    public void setCoordinateNorthing(String coordinateNorthing) {
        this.coordinateNorthing = coordinateNorthing;
    }

    public String getCoordinateEasting() {
        return coordinateEasting;
    }

    public void setCoordinateEasting(String coordinateEasting) {
        this.coordinateEasting = coordinateEasting;
    }

    public String getNorthDistance() {
        return northDistance;
    }

    public void setNorthDistance(String northDistance) {
        this.northDistance = northDistance;
    }

    public String getEastDistance() {
        return eastDistance;
    }

    public void setEastDistance(String eastDistance) {
        this.eastDistance = eastDistance;
    }

    public String getPositionIndicator() {
        return positionIndicator;
    }

    public void setPositionIndicator(String positionIndicator) {
        this.positionIndicator = positionIndicator;
    }

    public String getHeadingIndicator() {
        return headingIndicator;
    }

    public void setHeadingIndicator(String headingIndicator) {
        this.headingIndicator = headingIndicator;
    }

    public String getSvn() {
        return svn;
    }

    public void setSvn(String svn) {
        this.svn = svn;
    }

    public String getDiffAge() {
        return diffAge;
    }

    public void setDiffAge(String diffAge) {
        this.diffAge = diffAge;
    }

    public String getStationId() {
        return stationId;
    }

    public void setStationId(String stationId) {
        this.stationId = stationId;
    }

    public String getBaselineLength() {
        return baselineLength;
    }

    public void setBaselineLength(String baselineLength) {
        this.baselineLength = baselineLength;
    }

    public String getSolutionSv() {
        return solutionSv;
    }

    public void setSolutionSv(String solutionSv) {
        this.solutionSv = solutionSv;
    }

    public String getRolling() {
        return rolling;
    }

    public void setRolling(String rolling) {
        this.rolling = rolling;
    }
}
