package com.snct.analysis;

import com.snct.analysis.vo.AttitudeHbaseVo;
import com.snct.analysis.vo.AttitudeHbaseVo2;
import com.snct.analysis.vo.WindHbaseVo;
import com.snct.common.utils.DateUtils;
import com.snct.kafka.KafkaMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: Attitude  一秒多组
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class AttitudeAnalysis {
    protected static Logger logger = LoggerFactory.getLogger(AttitudeAnalysis.class);

    private static final String TEMP_KEY = "ATTITUDE_TEMP";

    private static int rollL, rollH, pitchL, pitchH, heaveL, heaveH, headingL, headingH;


    /**
     * 解析GPYBM格式的姿态数据
     *
     * @param kafkaMessage
     * @return
     */
    public static AttitudeHbaseVo2 getAttitudeList2(KafkaMessage kafkaMessage) {
        AttitudeHbaseVo2 attitudeHbaseVo2 = null;

        try {
            // 检查是否为GPYBM格式数据
            if (!kafkaMessage.getMsg().startsWith("$GPYBM")) {
                return null;
            }

            //当前时间
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() :
                    kafkaMessage.getInitialTime();

            // 分割数据，去掉校验和部分
            String dataWithoutChecksum = kafkaMessage.getMsg();
            if (dataWithoutChecksum.contains("*")) {
                dataWithoutChecksum = dataWithoutChecksum.substring(0, dataWithoutChecksum.lastIndexOf("*"));
            }

            String[] fields = dataWithoutChecksum.split(",", -1);
            if (fields.length < 24) {
                logger.info("姿态数据字段不足----{}", kafkaMessage.getMsg());
                return null;
            }

            attitudeHbaseVo2 = new AttitudeHbaseVo2();
            attitudeHbaseVo2.setInitialTime(DateUtils.fetchWholeSecond(currentTime).toString());
            attitudeHbaseVo2.setInitialBjTime(DateUtils.parseTimeToDate(currentTime, "yyyy-MM-dd HH:mm:ss"));

            // 按照对照表解析各个字段
            attitudeHbaseVo2.setSerialNo(fields[1]);           // 设备序列号
            attitudeHbaseVo2.setUtc(fields[2]);               // UTC时间
            attitudeHbaseVo2.setLat(fields[3].replace("+", ""));               // 纬度
            attitudeHbaseVo2.setLon(fields[4].replace("+", ""));               // 经度
            attitudeHbaseVo2.setElpHeight(fields[5]);         // 椭球高
            attitudeHbaseVo2.setHeading(fields[6]);           // 航向角
            attitudeHbaseVo2.setPitch(fields[7]);             // 俯仰角
            attitudeHbaseVo2.setVelN(fields[8]);              // 北方向速度
            attitudeHbaseVo2.setVelE(fields[9]);              // 东方向速度
            attitudeHbaseVo2.setVelD(fields[10]);             // 地向速度
            attitudeHbaseVo2.setVelG(fields[11]);             // 地面速度
            attitudeHbaseVo2.setCoordinateNorthing(fields[12]); // 高精度坐标北向
            attitudeHbaseVo2.setCoordinateEasting(fields[13]);  // 高精度坐标东向
            attitudeHbaseVo2.setNorthDistance(fields[14]);    // 北距离
            attitudeHbaseVo2.setEastDistance(fields[15]);     // 东距离
            attitudeHbaseVo2.setPositionIndicator(fields[16]); // 定位指示状态
            attitudeHbaseVo2.setHeadingIndicator(fields[17]);  // 定向指示状态
            attitudeHbaseVo2.setSvn(fields[18]);              // 主导天线收星数
            attitudeHbaseVo2.setDiffAge(fields[19]);          // 差分延迟
            attitudeHbaseVo2.setStationId(fields[20]);        // 基准站ID
            attitudeHbaseVo2.setBaselineLength(fields[21]);   // 基线长度
            attitudeHbaseVo2.setSolutionSv(fields[22]);       // 从站参与解算的卫星数
            attitudeHbaseVo2.setRolling(fields[23]);          // 横滚角

        } catch (Exception e) {
            logger.error("姿态数据解析出错,---{}", e);
        }

        return attitudeHbaseVo2;
    }


    /**
     * 从一组attitude数据中，取得有用的字段
     *
     * @param kafkaMessage
     * @return
     */
    public static AttitudeHbaseVo getAttitudeList(KafkaMessage kafkaMessage) {

        AttitudeHbaseVo attitudeHbaseVo = null;

        try {
            //当前时间
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime();

            // 判断时间是否为同一秒
            boolean isSame = BaseAnalysis.isSameTime(TEMP_KEY, kafkaMessage.getCode(), currentTime);

            if (!isSame) {
                String[] attitude = kafkaMessage.getMsg().split(",", -1);
                if (attitude.length < 10) {
                    logger.info("attitude--垃圾数据----{}", kafkaMessage.getMsg());
                    return null;
                }
                //多组取第一组
                for (int i = 0; i < attitude.length; i++) {
                    attitudeHbaseVo = new AttitudeHbaseVo();
                    attitudeHbaseVo.setInitialTime(DateUtils.fetchWholeSecond(currentTime).toString());
                    rollL = Integer.valueOf(attitude[2]);
                    rollH = Integer.valueOf(attitude[3]);
                    pitchL = Integer.valueOf(attitude[4]);
                    pitchH = Integer.valueOf(attitude[5]);
                    heaveL = Integer.valueOf(attitude[6]);
                    heaveH = Integer.valueOf(attitude[7]);
                    headingL = Integer.valueOf(attitude[8]);
                    headingH = Integer.valueOf(attitude[9]);
                    BigDecimal bigDecimal = new BigDecimal(String.valueOf(0.01));
                    attitudeHbaseVo.setRoll(new BigDecimal(turn2Signed(rollH << 8 | rollL)).multiply(bigDecimal).toString());
                    attitudeHbaseVo.setPitch(new BigDecimal(turn2Signed(pitchH << 8 | pitchL)).multiply(bigDecimal).toString());
                    attitudeHbaseVo.setHeave(new BigDecimal(turn2Signed(heaveH << 8 | heaveL)).multiply(bigDecimal).toString());
                    attitudeHbaseVo.setHeading(new BigDecimal(turn2Signed(headingH << 8 | headingL)).multiply(bigDecimal).toString());
                }

                BaseAnalysis.updateTimeTemp(TEMP_KEY, kafkaMessage.getCode(), currentTime);
            }
        } catch (Exception e) {
            logger.error("attitude解析出错,---{}", e);
        }

        return attitudeHbaseVo;
    }

    /**
     * 解析预览
     *
     * @param kafkaMessage
     * @return
     */
    public static AttitudeHbaseVo getParseAttitudeList(List<KafkaMessage> kafkaMessage) {

        AttitudeHbaseVo attitudeHbaseVo = null;

        try {
            for (KafkaMessage kafkaMessage1:
                    kafkaMessage) {
            //当前时间
//            long currentTime = kafkaMessage1.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage1.getInitialTime();

            // 判断时间是否为同一秒
//            boolean isSame = BaseAnalysis.isSameTime(TEMP_KEY, kafkaMessage.getCode(), currentTime);

//            if (!isSame) {
                String[] attitude = kafkaMessage1.getMsg().split(",", -1);
                if (attitude.length < 10) {
                    logger.info("attitude--垃圾数据----{}", kafkaMessage1.getMsg());
                    continue;
                }
                //多组取第一组
                for (int i = 0; i < attitude.length; i++) {
                    attitudeHbaseVo = new AttitudeHbaseVo();
//                    attitudeHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(currentTime,"yyyy-MM-dd HH:mm:ss").toString());
                    rollL = Integer.valueOf(attitude[2]);
                    rollH = Integer.valueOf(attitude[3]);
                    pitchL = Integer.valueOf(attitude[4]);
                    pitchH = Integer.valueOf(attitude[5]);
                    heaveL = Integer.valueOf(attitude[6]);
                    heaveH = Integer.valueOf(attitude[7]);
                    headingL = Integer.valueOf(attitude[8]);
                    headingH = Integer.valueOf(attitude[9]);
                    BigDecimal bigDecimal = new BigDecimal(String.valueOf(0.01));
                    attitudeHbaseVo.setRoll(new BigDecimal(turn2Signed(rollH << 8 | rollL)).multiply(bigDecimal).toString());
                    attitudeHbaseVo.setPitch(new BigDecimal(turn2Signed(pitchH << 8 | pitchL)).multiply(bigDecimal).toString());
                    attitudeHbaseVo.setHeave(new BigDecimal(turn2Signed(heaveH << 8 | heaveL)).multiply(bigDecimal).toString());
                    attitudeHbaseVo.setHeading(new BigDecimal(turn2Signed(headingH << 8 | headingL)).multiply(bigDecimal).toString());
                }
        }
            if (AwsAnalysis.checkObjAllFieldsIsNull(attitudeHbaseVo)){
                attitudeHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss").toString());
                attitudeHbaseVo.setInitialTime(null);
            }else{
                return null;
            }
//                BaseAnalysis.updateTimeTemp(TEMP_KEY, kafkaMessage.getCode(), currentTime);
//            }
        } catch (Exception e) {
            logger.error("attitude解析出错,---{}", e);
        }

        return attitudeHbaseVo;
    }

    /**
     * 计算成有符号的
     *
     * @param num
     * @return
     */
    private static int turn2Signed(int num) {
        if (num > 32767) {
            return num - 65535;
        }
        return num;
    }

}
