package com.snct.analysis;

import com.snct.kafka.KafkaMessage;
import com.snct.analysis.vo.RacduHbaseVo;
import com.snct.common.utils.DateUtils;
import com.snct.analysis.domain.racdu.Racdu;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RacduAnalysis {
    protected static Logger logger = LoggerFactory.getLogger(RacduAnalysis.class);

    private static final String TEMP_KEY = "RACDU_TEMP";

    /**
     * 从一组racdu1数据中，取得有用的字段
     *
     * @param kafkaMessage
     * @return
     */
    public static RacduHbaseVo getRacduList(KafkaMessage kafkaMessage) {
        RacduHbaseVo racduHbaseVo = null;

        List<String> temp = BaseAnalysis.getTempList(TEMP_KEY, kafkaMessage.getCode());
        if (temp == null) {
            temp = new ArrayList<>();
        }

        try {
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime();

            // 判断时间是否为同一秒
            boolean isSame = BaseAnalysis.isSameTime(TEMP_KEY, kafkaMessage.getCode(), currentTime);

            // 同一秒的数据不做发送，在下一秒后再一起合并
            if (!CollectionUtils.isEmpty(temp) && kafkaMessage.getMsg().startsWith("$RIRSA") && !isSame) {
                racduHbaseVo = new RacduHbaseVo();
                racduHbaseVo.setInitialTime(DateUtils.fetchWholeSecond(currentTime).toString());
                for (String line : temp) {
                    translateLine(racduHbaseVo, line);
                }
                temp.clear();
            }

            temp.add(kafkaMessage.getMsg());

            BaseAnalysis.updateTemp(TEMP_KEY, kafkaMessage.getCode(), temp, currentTime);
        } catch (Exception e) {
            logger.error("racdu解析出错,---{}", e);
            temp.clear();
        }
        return racduHbaseVo;
    }

    /**
     * 解析预览
     *
     * @param kafkaMessage
     * @return
     */
    public static RacduHbaseVo getParseRacduList(List<KafkaMessage> kafkaMessage) {
        RacduHbaseVo racduHbaseVo = null;

        List<String> temp = new ArrayList<>();
        for (KafkaMessage kafkaMessage1:
                kafkaMessage) {
            if (kafkaMessage1.getMsg().startsWith("$RIRSA")){
                temp.add(kafkaMessage1.getMsg());
            }

        }

        try {
            long currentTime = kafkaMessage.get(0).getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.get(0).getInitialTime();

            // 判断时间是否为同一秒
//            boolean isSame = BaseAnalysis.isSameTime(TEMP_KEY, kafkaMessage.getCode(), currentTime);

            // 同一秒的数据不做发送，在下一秒后再一起合并
            if (!CollectionUtils.isEmpty(temp)) {
                racduHbaseVo = new RacduHbaseVo();
//                racduHbaseVo.setInitialTime(DateUtils.parseTimeToDate(currentTime,"yyyy-MM-dd HH:mm:ss").toString());
                for (String line : temp) {
                    translateLine(racduHbaseVo, line);
                }
                if (AwsAnalysis.checkObjAllFieldsIsNull(racduHbaseVo)){
                    racduHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss").toString());
                    racduHbaseVo.setInitialTime(null);
                }else{
                    return null;
                }
                temp.clear();
            }

//            temp.add(kafkaMessage.getMsg());
//
//            BaseAnalysis.updateTemp(TEMP_KEY, kafkaMessage.getCode(), temp, currentTime);
        } catch (Exception e) {
            logger.error("racdu解析出错,---{}", e);
            temp.clear();
        }
        return racduHbaseVo;
    }

    /**
     * 转换一行数据
     *
     * @param racduHbaseVo line
     * @return
     */
    private static void translateLine(RacduHbaseVo racduHbaseVo, String line) {
        Racdu racdu = new Racdu();
        racdu.dataAnalysis(line);
        racduHbaseVo.setAutopilotRate(racdu.getAutopilotRate());
    }

}
