package com.snct.analysis;

import com.snct.analysis.enums.DeviceTypeEnum;
import com.snct.common.utils.DateUtils;
import com.snct.kafka.KafkaMessage;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

import static com.snct.analysis.enums.DeviceTypeEnum.*;

/**
 * class
 *
 * <AUTHOR>
 */
public class BaseAnalysis {
    private static Map<String, List<String>> tempMap = new ConcurrentHashMap<>();

    public static void clearTemp() {
        tempMap.clear();
    }

    private static Map<String, Long> lastTimeMap = new ConcurrentHashMap<>();

    static List<String> getTempList(String key, String code) {
        return tempMap.get(getTempMapKey(key, code));
    }


    public static Object analysisData(KafkaMessage kafkaMessage) {
        switch (DeviceTypeEnum.getByValue(kafkaMessage.getType())) {
            case GPS: {
                return GpsAnalysis.getGpsList(kafkaMessage);
            }
            case AWS: {
                return AwsAnalysis.getAwsList(kafkaMessage);
            }
            case SBE21: {
                return Sbe21Analysis.getSbe21List(kafkaMessage);
            }
            case GO8050: {
                return Co2Analysis.getCo2List(kafkaMessage);
            }
            case COMPASS: {
                return CompassAnalysis.getCompassList(kafkaMessage);
            }
            case LOG: {
                return LogAnalysis.getLogList(kafkaMessage);
            }
            case RA_CDU1: {
                return RacduAnalysis.getRacduList(kafkaMessage);
            }
            case RA_CDU2: {
                return RacduAnalysis.getRacduList(kafkaMessage);
            }
            case ATTITUDE: {
                return AttitudeAnalysis.getAttitudeList(kafkaMessage);
            }
            case EA600: {
                return Ea600Analysis.getEa600List(kafkaMessage);
            }
            case ECHO: {
                return DetectorAnalysis.getDetectorList(kafkaMessage);
            }
            case WIND: {
                return WindAnalysis.getWindList(kafkaMessage);
            }
            case GPS_IN: {
                return GpsInAnalysis.getGpsNewList(kafkaMessage);
            }
            case COMPASS_IN: {
                return CompassInAnalysis.getCompassNew(kafkaMessage);
            }
            default: {
                return null;
            }
        }
    }

    /**
     * 根据消息类型将数据分发到不同的专用分析器
     * @param kafkaMessage Kafka消息对象
     * @param c 处理控制参数
     * @param s 附加状态对象
     * @return 分析结果
     */
    public static synchronized Object analysisData(KafkaMessage kafkaMessage, int c, Object s) {
        if (kafkaMessage.getType().intValue() == 66) {
        }
        if (kafkaMessage.getType().intValue() == 67) {
        }
        switch (Objects.requireNonNull(getByValue(kafkaMessage.getType()))) {
            case GPS: // GPS类型数据
                return GpsAnalysis.getGpsList2(kafkaMessage);
            case ATTITUDE: // 姿态类型数据
                return AttitudeAnalysis.getAttitudeList2(kafkaMessage);
            case WIND:  // 风力类型数据
                return WindAnalysis.getWindList2(kafkaMessage);
            case AWS:  // AWS类型数据
                if (c == 1) {
                    return AwsAnalysis.getAwsList2(kafkaMessage, s);
                }
                return AwsAnalysis.getAwsNewList(kafkaMessage);
            default:
                return null;
        }
    }

    /**
     * 解析预览-单独处理
     *
     * @param kafkaMessage
     * @return
     */
    public static Object analysisParseData(List<KafkaMessage> kafkaMessage) {
        switch (DeviceTypeEnum.getByValue(kafkaMessage.get(0).getType())) {
            case GPS: {
                return GpsAnalysis.getParseGpsList(kafkaMessage);
            }
            case AWS: {
                return AwsAnalysis.getParseAwsList(kafkaMessage);
            }
            case SBE21: {
                return Sbe21Analysis.getParseSbe21List(kafkaMessage);
            }
            case GO8050: {
                return Co2Analysis.getParseCo2List(kafkaMessage);
            }
            case COMPASS: {
                return CompassAnalysis.getParseCompassList(kafkaMessage);
            }
            case LOG: {
                return LogAnalysis.getParseLogList(kafkaMessage);
            }
            case RA_CDU1: {
                return RacduAnalysis.getParseRacduList(kafkaMessage);
            }
            case RA_CDU2: {
                return RacduAnalysis.getParseRacduList(kafkaMessage);
            }
            case ATTITUDE: {
                return AttitudeAnalysis.getParseAttitudeList(kafkaMessage);
            }
            case EA600: {
                return Ea600Analysis.getParseEa600List(kafkaMessage);
            }
            case ECHO: {
                return DetectorAnalysis.getParseDetectorList(kafkaMessage);
            }
            case WIND: {
                return WindAnalysis.getParseWindList(kafkaMessage);
            }
            case GPS_IN: {
                return GpsInAnalysis.getGpsNewParseList(kafkaMessage.get(0));
            }
            case COMPASS_IN: {
                return CompassInAnalysis.getCompassParseNew(kafkaMessage.get(0));
            }
            default: {
                return null;
            }
        }
    }


    static void updateTemp(String key, String code, List<String> tempList, Long lastTime) {
        tempMap.put(getTempMapKey(key, code), tempList);
        if (lastTime != null) {
            updateTimeTemp(key, code, lastTime);
        }
    }

    static void updateTimeTemp(String key, String code, Long lastTime) {
        lastTimeMap.put(getLastTimeMapKey(key, code), DateUtils.fetchWholeSecond(lastTime));
    }

    static boolean isSameTime(String key, String code, Long time) {
        if (lastTimeMap.get(getLastTimeMapKey(key, code)) == null) {
            return false;
        }

        return DateUtils.fetchWholeSecond(time).equals(lastTimeMap.get(getLastTimeMapKey(key, code)));
    }

    private static String getTempMapKey(String key, String code) {
        return "DATA_" + key + "_" + code;
    }

    private static String getLastTimeMapKey(String key, String code) {
        return "LAST_TIME_" + key + "_" + code;
    }
}
