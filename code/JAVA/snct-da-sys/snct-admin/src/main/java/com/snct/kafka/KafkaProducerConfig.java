package com.snct.kafka;

import com.snct.common.config.SnctConfig;
import com.snct.system.domain.SysConfig;
import com.snct.system.service.ISysConfigService;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class KafkaProducerConfig {

    @Autowired
    private ISysConfigService configService;

    static String net = "N";

    // 完全通过代码配置 Kafka 生产者
    @Bean
    public ProducerFactory<String, String> producerFactory() {

        SysConfig sysConfignet = configService.selectConfigByConfigKeyKey("net_transfer_function");
        net = sysConfignet.getConfigValue();

        if("Y".equals(net)) {
            Map<String, Object> configProps = new HashMap<>();
            // 基本配置
            configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, SnctConfig.getKafkaServerTrl());   //连接kafka地址
            configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
            configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

            // Kafka 2.8.0 特定优化配置
            configProps.put(ProducerConfig.ACKS_CONFIG, "all"); // 所有副本确认
            configProps.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true); // 启用幂等性
            configProps.put(ProducerConfig.RETRIES_CONFIG, 5); // 重试次数
            configProps.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 5); // 并发请求数
            configProps.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "snappy"); // 压缩算法
            configProps.put(ProducerConfig.LINGER_MS_CONFIG, 20); // 批量发送延迟
            configProps.put(ProducerConfig.BATCH_SIZE_CONFIG, 32768); // 批量大小 32KB
            configProps.put("enable.idempotence", "false");
            // 事务配置（可选）
            //configProps.put(ProducerConfig.TRANSACTIONAL_ID_CONFIG, "kafka-producer-1");
            return new DefaultKafkaProducerFactory<>(configProps);
        }
        return null;

    }

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        if("Y".equals(net)){
            KafkaTemplate<String, String> template = new KafkaTemplate<>(producerFactory());
            // 设置默认主题
            //template.setDefaultTopic("spring-kafka-topic-test");
            KafkaService.kafkaTemplate = template;
            return template;
        }
        return null;
    }
}