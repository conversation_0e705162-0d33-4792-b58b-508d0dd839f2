package com.snct.web.controller.api.dto;

import java.io.Serializable;

/**
 * 设备Token信息
 * 
 * <AUTHOR>
 */
public class DeviceTokenInfo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**
     * 设备账号
     */
    private String deviceAccount;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipAddress;

    public DeviceTokenInfo()
    {
    }

    public DeviceTokenInfo(String deviceAccount, Long createTime, Long expireTime, String ipAddress)
    {
        this.deviceAccount = deviceAccount;
        this.createTime = createTime;
        this.expireTime = expireTime;
        this.ipAddress = ipAddress;
    }

    public String getDeviceAccount()
    {
        return deviceAccount;
    }

    public void setDeviceAccount(String deviceAccount)
    {
        this.deviceAccount = deviceAccount;
    }

    public Long getCreateTime()
    {
        return createTime;
    }

    public void setCreateTime(Long createTime)
    {
        this.createTime = createTime;
    }

    public Long getExpireTime()
    {
        return expireTime;
    }

    public void setExpireTime(Long expireTime)
    {
        this.expireTime = expireTime;
    }

    public String getIpAddress()
    {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress)
    {
        this.ipAddress = ipAddress;
    }

    /**
     * 检查token是否过期
     */
    public boolean isExpired()
    {
        return System.currentTimeMillis() > expireTime;
    }

    @Override
    public String toString()
    {
        return "DeviceTokenInfo{" +
                "deviceAccount='" + deviceAccount + '\'' +
                ", createTime=" + createTime +
                ", expireTime=" + expireTime +
                ", ipAddress='" + ipAddress + '\'' +
                '}';
    }
}
