package com.snct.device.service;

import com.snct.device.manager.DeviceConnectionManager;
import com.snct.kafka.KafkaService;
import com.snct.netty.DeviceMessageListener;
import com.snct.netty.NettyClient;
import com.snct.netty.pdu.PduDeviceHandler;
import com.snct.system.domain.Device;
import com.snct.system.domain.msg.BuMsgPdu;
import com.snct.system.domain.msg.BuMsgPduOut;
import com.snct.system.service.IDeviceService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * PDU设备服务
 * 管理PDU设备的连接和数据交互
 */
@Service
public class PduDeviceService {

    private static final Logger logger = LoggerFactory.getLogger(PduDeviceService.class);

    // PDU设备客户端列表
    private final List<NettyClient> pduClients = new ArrayList<>();
    
    // PDU设备处理器列表
    private final List<PduDeviceHandler> pduHandlers = new ArrayList<>();
    
    // 命令批次发送间隔(毫秒)
    private static final int COMMAND_DELAY_MS = 100;
    
    // 上次发送查询命令的时间
    private long lastQueryTime = 0;
    
    // 最小查询间隔(毫秒)，防止过于频繁地查询
    private static final long MIN_QUERY_INTERVAL = 500;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private DeviceConnectionManager connectionManager;
    
    /**
     * 初始化所有PDU设备连接
     */
    public void initPduDevices() {
        try {
            logger.debug("开始初始化PDU设备连接...");

            Device queryDevice = new Device();
            queryDevice.setType(51);
            queryDevice.setEnable(1);
            List<Device> pduDevices = deviceService.selectDeviceList(queryDevice);
            
            if (pduDevices == null || pduDevices.isEmpty()) {
                logger.info("未找到可用的PDU设备，跳过初始化");
                return;
            }

            for (Device pduDevice : pduDevices) {
                String deviceCode = pduDevice.getCode();
                
                boolean success = addPduDevice(pduDevice);
                
                if (success) {
                    // 添加设备消息监听器用于日志记录
                    addMessageListener(new DeviceMessageListener() {
                        @Override
                        public void onMessageReceived(String messageType, String hexData, Object parsedData) {
                            logger.debug("收到PDU设备[{}]消息 - 类型: {}, 数据: {}", deviceCode, messageType, hexData);
                        }
                    });
                    
                    logger.info("PDU设备[{}]初始化成功，IP:{}, 端口:{}, SN:{}", 
                        deviceCode, pduDevice.getIp(), pduDevice.getPort(), pduDevice.getSn());
                } else {
                    logger.error("PDU设备[{}]初始化失败，IP:{}, 端口:{}", 
                        deviceCode, pduDevice.getIp(), pduDevice.getPort());
                }
            }
            
            logger.info("PDU设备初始化完成，共初始化{}个设备", pduHandlers.size());
        } catch (Exception e) {
            logger.error("PDU设备初始化异常", e);
        }
    }

    /**
     * 添加PDU设备
     * 
     * @param device PDU设备对象
     * @return 是否成功添加
     */
    public boolean addPduDevice(Device device) {
        if (device == null) {
            logger.error("设备对象为空，无法添加PDU设备");
            return false;
        }
        
        String host = device.getIp();
        int port = device.getPort();
        String deviceId = device.getCode();
        
        try {
            // 创建PDU设备处理器
            PduDeviceHandler pduHandler = new PduDeviceHandler();

            // 设置Kafka服务
            pduHandler.setKafkaService(kafkaService);
            
            // 设置设备对象
            pduHandler.setDevice(device);
            
            // 创建NettyClient
            NettyClient client = new NettyClient(host, port, deviceId, pduHandler);
            
            // 连接PDU设备
            boolean connected = client.connect();
            if (connected) {
                pduClients.add(client);
                pduHandlers.add(pduHandler);

                // 更新设备连接状态为连接
                connectionManager.updateDeviceConnectionStatus(device,
                    DeviceConnectionManager.ConnectionStatus.CONNECTED,
                    DeviceConnectionManager.ProtocolType.TCP);

                logger.info("添加PDU设备成功: {}:{}, SN: {}", host, port, device.getSn());
                return true;
            } else {
                // 更新设备连接状态为断开
                connectionManager.updateDeviceConnectionStatus(device,
                    DeviceConnectionManager.ConnectionStatus.DISCONNECTED,
                    DeviceConnectionManager.ProtocolType.TCP);

                logger.error("添加PDU设备失败: {}:{}", host, port);
                return false;
            }
        } catch (Exception e) {
            // 异常时更新设备连接状态为未知
            connectionManager.updateDeviceConnectionStatus(device,
                DeviceConnectionManager.ConnectionStatus.UNKNOWN,
                DeviceConnectionManager.ProtocolType.TCP);

            logger.error("添加PDU设备异常: {}:{}", host, port, e);
            return false;
        }
    }
    
    /**
     * 添加消息监听器
     * 
     * @param listener 消息监听器
     */
    public void addMessageListener(DeviceMessageListener listener) {
        for (PduDeviceHandler handler : pduHandlers) {
            handler.addMessageListener(listener);
        }
    }

    
    /**
     * 批量发送查询命令到所有PDU设备
     * 每个设备的所有查询命令作为一个批次，并分配唯一批次号
     * 
     * @return 成功发送命令的设备数量
     */
    public int sendAllQueryCommands() {
        // 检查是否达到最小查询间隔
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastQueryTime < MIN_QUERY_INTERVAL) {
            logger.debug("查询频率过高，跳过本次查询，上次查询时间: {}", lastQueryTime);
            return 0;
        }
        
        // 更新最后查询时间
        lastQueryTime = currentTime;

        // 检查连接状态，如果有断开的设备，尝试重连
        int connectedCount = checkConnectionStatus();
        if (connectedCount == 0) {
            logger.warn("没有已连接的PDU设备，尝试重连");
            reconnectDisconnectedDevices();
        } else if (connectedCount < pduClients.size()) {
            logger.info("部分PDU设备未连接，尝试重连断开的设备");
            reconnectDisconnectedDevices();
        }

        int successCount = 0;

        for (PduDeviceHandler handler : pduHandlers) {
            try {
                boolean success = handler.sendAllQueryCommands(COMMAND_DELAY_MS);
                if (success) {
                    successCount++;
                }
            } catch (Exception e) {
                logger.error("发送PDU查询命令异常", e);
            }
        }
        
        if (successCount > 0) {
            logger.debug("批量发送查询命令完成");
        } else {
            logger.warn("批量发送查询命令失败，所有设备均发送失败");
        }
        
        return successCount;
    }
    
    /**
     * 获取所有PDU设备数据
     * 
     * @return PDU设备数据列表
     */
    public List<BuMsgPdu> getAllPduData() {
        List<BuMsgPdu> dataList = new ArrayList<>();
        for (PduDeviceHandler handler : pduHandlers) {
            BuMsgPdu pduData = handler.getPduData();
            if (pduData != null) {
                dataList.add(pduData);
            }
        }
        return dataList;
    }
    
    /**
     * 获取所有PDU设备输出通道数据
     * 
     * @return PDU设备输出通道数据列表
     */
    public List<Map<Integer, BuMsgPduOut>> getAllPduOutData() {
        List<Map<Integer, BuMsgPduOut>> dataList = new ArrayList<>();
        for (PduDeviceHandler handler : pduHandlers) {
            Map<Integer, BuMsgPduOut> outData = handler.getPduOutData();
            if (outData != null && !outData.isEmpty()) {
                dataList.add(outData);
            }
        }
        return dataList;
    }
    
    /**
     * 获取设备数量
     */
    public int getDeviceCount() {
        return pduHandlers.size();
    }
    
    /**
     * 关闭所有PDU设备连接
     */
    public void shutdown() {
        for (NettyClient client : pduClients) {
            try {
                client.shutdown();
            } catch (Exception e) {
                logger.error("关闭PDU设备连接异常: {}", client.getDeviceId(), e);
            }
        }
        pduClients.clear();
        pduHandlers.clear();
    }
    
    /**
     * 检查所有PDU设备连接状态
     *
     * @return 已连接的设备数量
     */
    public int checkConnectionStatus() {
        int connectedCount = 0;

        if (pduClients.isEmpty()) {
            return 0;
        }

        for (int i = 0; i < pduClients.size() && i < pduHandlers.size(); i++) {
            NettyClient client = pduClients.get(i);
            PduDeviceHandler handler = pduHandlers.get(i);
            Device device = handler.getDevice();

            boolean connected = client.isConnected();
            String deviceId = client.getDeviceId();

            if (connected) {
                connectedCount++;
                logger.debug("PDU设备[{}]连接正常", deviceId);

                // 更新统一连接状态管理
                if (device != null) {
                    connectionManager.updateDeviceConnectionStatus(device,
                        DeviceConnectionManager.ConnectionStatus.CONNECTED,
                        DeviceConnectionManager.ProtocolType.TCP);
                }
            } else {
                logger.warn("PDU设备[{}]连接已断开", deviceId);

                // 更新统一连接状态管理
                if (device != null) {
                    connectionManager.updateDeviceConnectionStatus(device,
                        DeviceConnectionManager.ConnectionStatus.DISCONNECTED,
                        DeviceConnectionManager.ProtocolType.TCP);
                }
            }
        }

        logger.debug("PDU设备连接状态检查 - 总数: {}, 已连接: {}, 未连接: {}",
            pduClients.size(), connectedCount, pduClients.size() - connectedCount);

        return connectedCount;
    }
    
    /**
     * 尝试重连所有断开连接的PDU设备
     * 
     * @return 成功重连的设备数量
     */
    public int reconnectDisconnectedDevices() {
        int reconnectedCount = 0;
        
        if (pduClients.isEmpty()) {
            // 如果没有已初始化的设备，尝试重新初始化所有设备
            logger.debug("没有已初始化的PDU设备，尝试重新初始化所有设备");
            initPduDevices();
            return checkConnectionStatus();
        }
        
        logger.debug("开始尝试重连断开的PDU设备...");
        
        for (NettyClient client : pduClients) {
            if (!client.isConnected()) {
                String deviceId = client.getDeviceId();
                String host = client.getHost();
                int port = client.getPort();
                
                logger.debug("尝试重连PDU设备[{}]: {}:{}", deviceId, host, port);
                
                boolean success = client.connect();
                
                if (success) {
                    reconnectedCount++;
                    logger.debug("PDU设备[{}]重连成功", deviceId);
                } else {
                    logger.error("PDU设备[{}]重连失败", deviceId);
                }
            }
        }
        
        if (reconnectedCount > 0) {
            logger.debug("PDU设备重连完成，成功重连{}个设备", reconnectedCount);
        } else {
            logger.warn("PDU设备重连完成，没有设备重连成功");
        }
        
        return reconnectedCount;
    }

    /**
     * 重新加载PDU设备配置（支持热更新）
     * 关闭所有现有连接并重新初始化所有PDU设备
     */
    public void reloadPduDevices() {
        logger.info("开始重新加载PDU设备配置");

        try {
            // 1. 关闭所有现有的PDU连接
            logger.info("关闭所有现有的PDU设备连接");
            closeAllPduConnections();

            // 2. 清空设备列表
            pduClients.clear();
            pduHandlers.clear();

            // 3. 重新初始化所有PDU设备
            logger.info("重新初始化PDU设备连接");
            initPduDevices();

            logger.info("PDU设备配置重新加载完成，当前连接数量: {}", pduClients.size());

        } catch (Exception e) {
            logger.error("重新加载PDU设备配置时发生异常", e);
            throw new RuntimeException("重新加载PDU设备配置失败", e);
        }
    }

    /**
     * 关闭所有PDU设备连接
     */
    private void closeAllPduConnections() {
        if (pduClients.isEmpty()) {
            logger.info("没有需要关闭的PDU设备连接");
            return;
        }

        logger.info("开始关闭{}个PDU设备连接", pduClients.size());

        for (NettyClient client : pduClients) {
            try {
                if (client.isConnected()) {
                    String deviceId = client.getDeviceId();
                    client.disconnect();
                    logger.info("PDU设备[{}]连接已关闭", deviceId);
                }
            } catch (Exception e) {
                logger.error("关闭PDU设备连接时发生异常", e);
            }
        }

        logger.info("所有PDU设备连接关闭完成");
    }

    /**
     * 获取当前活跃的PDU设备连接数量
     */
    public int getActivePduConnectionCount() {
        return (int) pduClients.stream().filter(NettyClient::isConnected).count();
    }
}