package com.snct.analysis.vo;

import com.snct.common.annotation.Excel;
import com.snct.common.annotation.Excel;

/**
 * @description: 风速风向仪
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class WindHbaseVo {

    private String id;
    /**
     * 录入时间
     */
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    private String initialBjTime;

    /**
     * 相对风向
     */
    @Excel(name="相对风向")
    private String relativeWind;

    /**
     * 相对风向标识
     */
    @Excel(name="相对风向标识")
    private String windLogoR;

    /**
     * 相对风速
     */
    @Excel(name="相对风速")
    private String relativeWindSpeed;

    /**
     * 真实风向
     */
    @Excel(name="真实风向")
    private String trueWind;

    /**
     * 真实风速
     */
    @Excel(name="真实风速")
    private String trueWindSpeed;

    /**
     * 真实风向标识
     */
    @Excel(name="真实风向标识")
    private String windLogoT;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getRelativeWind() {
        return relativeWind;
    }

    public void setRelativeWind(String relativeWind) {
        this.relativeWind = relativeWind;
    }

    public String getWindLogoR() {
        return windLogoR;
    }

    public void setWindLogoR(String windLogoR) {
        this.windLogoR = windLogoR;
    }

    public String getRelativeWindSpeed() {
        return relativeWindSpeed;
    }

    public void setRelativeWindSpeed(String relativeWindSpeed) {
        this.relativeWindSpeed = relativeWindSpeed;
    }

    public String getTrueWind() {
        return trueWind;
    }

    public void setTrueWind(String trueWind) {
        this.trueWind = trueWind;
    }

    public String getTrueWindSpeed() {
        return trueWindSpeed;
    }

    public void setTrueWindSpeed(String trueWindSpeed) {
        this.trueWindSpeed = trueWindSpeed;
    }

    public String getWindLogoT() {
        return windLogoT;
    }

    public void setWindLogoT(String windLogoT) {
        this.windLogoT = windLogoT;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

}
