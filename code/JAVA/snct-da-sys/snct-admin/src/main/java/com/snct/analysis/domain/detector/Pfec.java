package com.snct.analysis.domain.detector;

import com.snct.system.domain.msg.Instrument;

/**
 * @description: 探测仪的$SDDBT数据 传感器以下深度
 * Example:$PFEC,SDmsi,1,2,M,S,F,200.0,0.63,6.00,0.00*1A
 * <p>
 * 逗号隔开
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Pfec extends Instrument {
    /**
     * 1.SDmsi
     */
    private String pfName;
    /**
     * 2.
     */
    private String pfOne;
    /**
     * 3.
     */
    private String pfTwo;

    /**
     * 4.单位M
     */
    private String unitM;
    /**
     * 5.单位s
     */
    private String unitS;
    /**
     * 6.单位f
     */
    private String unitF;

    /**
     * 7.
     */
    private String detctOne;
    /**
     * 8.
     */
    private String detctTwo;
    /**
     * 9.
     */
    private String detctTree;
    /**
     * 10.单位+校验码
     */
    private String statusUnit;

    @Override
    public void dataAnalysis(String dataStr) {
        String[] values = dataStr.split(",", -1);
        values = super.valuesTrim(values);
        try {
            if ("SDmsi".equals(values[1])) {
                this.pfName = values[1];
                this.pfOne = values[2];
                this.pfTwo = values[3];
                this.unitM = values[4];
                this.unitS = values[5];
                this.unitF = values[6] == null ? values[6] : "0.00";
                this.detctOne = values[7] == null ? values[7] : "0.00";
                this.detctTwo = values[8] == null ? values[8] : "0.00";
                this.detctTree = values[9] == null ? values[9] : "0.00";
                this.statusUnit = values[10];
            } else if (values[1].equals("xdr")) {
                this.pfName = values[1];
                this.pfOne = values[3];
                this.statusUnit = values[4];
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getPfName() {
        return pfName;
    }

    public void setPfName(String pfName) {
        this.pfName = pfName;
    }

    public String getPfOne() {
        return pfOne;
    }

    public void setPfOne(String pfOne) {
        this.pfOne = pfOne;
    }

    public String getPfTwo() {
        return pfTwo;
    }

    public void setPfTwo(String pfTwo) {
        this.pfTwo = pfTwo;
    }

    public String getUnitM() {
        return unitM;
    }

    public void setUnitM(String unitM) {
        this.unitM = unitM;
    }

    public String getUnitS() {
        return unitS;
    }

    public void setUnitS(String unitS) {
        this.unitS = unitS;
    }

    public String getUnitF() {
        return unitF;
    }

    public void setUnitF(String unitF) {
        this.unitF = unitF;
    }

    public String getDetctOne() {
        return detctOne;
    }

    public void setDetctOne(String detctOne) {
        this.detctOne = detctOne;
    }

    public String getDetctTwo() {
        return detctTwo;
    }

    public void setDetctTwo(String detctTwo) {
        this.detctTwo = detctTwo;
    }

    public String getDetctTree() {
        return detctTree;
    }

    public void setDetctTree(String detctTree) {
        this.detctTree = detctTree;
    }

    public String getStatusUnit() {
        return statusUnit;
    }

    public void setStatusUnit(String statusUnit) {
        this.statusUnit = statusUnit;
    }
}
