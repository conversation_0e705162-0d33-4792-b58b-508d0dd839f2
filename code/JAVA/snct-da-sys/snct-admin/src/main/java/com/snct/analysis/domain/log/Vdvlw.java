package com.snct.analysis.domain.log;

import com.snct.system.domain.msg.Instrument;

/**
 * @description: Log的$VDVLW数据  获取计程值
 * Example:$VDVLW,1012.90,N,1003.60,N,,,,*50
 * $VDVLW,<1>,N,77860.50,N,,,,*CS<CR><LF>
 * 逗号隔开
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Vdvlw extends Instrument {
    /**
     * 1.船舶航行总里程
     */
    private String totalShipMileage;

    @Override
    public void dataAnalysis(String dataStr) {
        String[] values = dataStr.split(",", -1);
        values = super.valuesTrim(values);
        totalShipMileage = values[1];
    }

    public String getTotalShipMileage() {
        return totalShipMileage;
    }

    public void setTotalShipMileage(String totalShipMileage) {
        this.totalShipMileage = totalShipMileage;
    }
}
