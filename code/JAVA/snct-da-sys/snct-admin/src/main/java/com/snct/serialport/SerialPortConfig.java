package com.snct.serialport;

import com.snct.system.domain.Device;

/**
 * 串口配置类
 */
public class SerialPortConfig {
    private String portName;
    private int baudRate;
    private int dataBits;
    private int stopBits;
    private int parity;
    private Device device;

    public SerialPortConfig(String portName, int baudRate, int dataBits, int stopBits, int parity, Device device) {
        this.portName = portName;
        this.baudRate = baudRate;
        this.dataBits = dataBits;
        this.stopBits = stopBits;
        this.parity = parity;
        this.device = device;
    }

    // Getters
    public String getPortName() {
        return portName;
    }

    public int getBaudRate() {
        return baudRate;
    }

    public int getDataBits() {
        return dataBits;
    }

    public int getStopBits() {
        return stopBits;
    }

    public int getParity() {
        return parity;
    }

    public Device getDevice() {
        return device;
    }
}