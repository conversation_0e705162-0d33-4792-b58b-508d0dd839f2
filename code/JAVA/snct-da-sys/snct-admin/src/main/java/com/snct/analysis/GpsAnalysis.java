package com.snct.analysis;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.snct.framework.aspectj.DataScopeAspect;
import com.snct.kafka.KafkaMessage;
import com.snct.analysis.vo.GpsHbaseVo;
import com.snct.common.utils.DateUtils;
import net.sf.marineapi.nmea.parser.SentenceFactory;
import net.sf.marineapi.nmea.sentence.*;
import net.sf.marineapi.nmea.util.Position;
import net.sf.marineapi.nmea.util.Time;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * class
 *
 * <AUTHOR>
 */
public class GpsAnalysis {

    protected static Logger logger = LoggerFactory.getLogger(GpsAnalysis.class);

    /**
     * 临时数据存储Map，合并后清空1
     */
    private static final String TEMP_KEY = "GPS_TEMP";

    // =====================新的=====================
    /**
     * 解析单条Kafka消息中的GPS数据
     * 主要处理$GNRMC或$BDRMC开头的NMEA格式数据
     *
     * @param kafkaMessage Kafka消息对象
     * @return 解析后的GPS数据对象
     */
    public static GpsHbaseVo getGpsList2(KafkaMessage kafkaMessage) {
        GpsHbaseVo gpsHbaseVo = null;
        if (StringUtils.isEmpty(kafkaMessage.getMsg())) {
            return null;
        }
        try {
        } catch (Exception e) {
            logger.error("GPS解析出错", e);
        }
        if (kafkaMessage.getMsg().startsWith("$GNRMC") || kafkaMessage.getMsg().startsWith("$BDRMC")) {
            gpsHbaseVo = new GpsHbaseVo();
            String[] values = valuesTrim(kafkaMessage.getMsg().split(",", -1));
            // 设置UTC时间
            gpsHbaseVo.setUtcTime(values[1].substring(0, 6));
            // 处理纬度和纬度半球
            if (values[4].equalsIgnoreCase("N")) {
                gpsHbaseVo.setLatitude(values[3] + "1");
            } else if (values[4].equalsIgnoreCase("S")) {
                gpsHbaseVo.setLatitude(values[3] + "3");
            }
            gpsHbaseVo.setLatitudeHemisphere(values[4]);
            // 处理经度和经度半球
            if (values[6].equalsIgnoreCase("E")) {
                gpsHbaseVo.setLongitude(values[5] + "2");
            } else if (values[6].equalsIgnoreCase("W")) {
                gpsHbaseVo.setLongitude(values[5] + "4");
            }
            gpsHbaseVo.setLongitudeHemisphere(values[6]);

            return gpsHbaseVo;
        }
        return null;
    }

    /**
     * 处理字符串数组，去除每个元素的前后空白
     *
     * @param values 原始字符串数组
     * @return 处理后的字符串数组
     */
    public static String[] valuesTrim(String[] values) {
        String[] result = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            result[i] = values[i].trim();
        }
        return result;
    }

    // =====================旧的=====================

    /**
     * 从一组GPS数据中，取得有用的字段
     *
     * @param kafkaMessage
     * @return
     */
    public static GpsHbaseVo getGpsList(KafkaMessage kafkaMessage) {
        GpsHbaseVo gpsHbaseVo = null;
        if (StringUtils.isEmpty(kafkaMessage.getMsg())) {
            return null;
        }

        List<String> temp = BaseAnalysis.getTempList(TEMP_KEY, kafkaMessage.getCode());
        if (temp == null) {
            temp = new ArrayList<>();
        }

        try {
            if (!CollectionUtils.isEmpty(temp) && kafkaMessage.getMsg().startsWith("$GPGGA")) {
                // 转换一个完整GPS数据
                gpsHbaseVo = translateWholeGps(temp);
                temp.clear();
            }

            if (!kafkaMessage.getMsg().contains("PSTT")) {
                temp.add(kafkaMessage.getMsg());
            }

            BaseAnalysis.updateTemp(TEMP_KEY, kafkaMessage.getCode(), temp, null);
        } catch (Exception e) {
            logger.error("GPS解析出错", e);
            temp.clear();
        }
        return gpsHbaseVo;
    }

    /**
     * 解析预览使用
     *
     * @param kafkaMessage
     * @return
     */
    public static GpsHbaseVo getParseGpsList(List<KafkaMessage> kafkaMessage) {
        GpsHbaseVo gpsHbaseVo = new GpsHbaseVo();
        List<String> temp = new ArrayList<>();
        List<String> lines = new ArrayList<>();
        for (KafkaMessage kafkaMessage1:
                kafkaMessage) {
            lines.add(kafkaMessage1.getMsg());

        }
        try {
                 // 转换一个完整GPS数据
            for (String line : lines) {
                try {
                    translateParseLine(gpsHbaseVo, line);
                } catch (Exception e) {
                    logger.error("GPS解析出错，--{}", e);
                }
            }
            if (AwsAnalysis.checkObjAllFieldsIsNull(gpsHbaseVo)){
                gpsHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss").toString());
            }else{
                return null;
            }
//            gpsHbaseVo = translateWholeGps(temp);

         } catch (Exception e) {
            logger.error("GPS解析出错", e);
         }
        temp.clear();
        return gpsHbaseVo;
    }

    /**
     * 转换一个完整GPS数据
     *
     * @param
     * @return
     */
    private static GpsHbaseVo translateWholeGps(List<String> lines) {
        GpsHbaseVo gpsHbaseVo = new GpsHbaseVo();
        logger.info("解析的全部数据----，{}", JSONObject.toJSONString(lines));

        for (String line : lines) {
            try {
                translateLine(gpsHbaseVo, line);
            } catch (Exception e) {
                logger.error("GPS解析出错，--{}", e);
            }
        }

        return gpsHbaseVo;
    }

    /**
     * 转换一行数据
     *
     * @param line
     * @return
     */
    private static void translateLine(GpsHbaseVo gpsHbaseVo, String line) {
        if (line.startsWith("@&")) {
            return;
        }
        SentenceFactory sf = SentenceFactory.getInstance();
        Sentence sentence = sf.createParser(line);

        switch (sentence.getSentenceId()) {
            //时间 位置 定位类型（全球地位信息）
            case "GGA": {
                GGASentence gga = (GGASentence) sentence;
                gpsHbaseVo.setUtcTime(gga.getTime().toString());

                Position position = gga.getPosition();

                gpsHbaseVo.setLatitude(String.valueOf(position.getLatitude()));
                gpsHbaseVo.setLatitudeHemisphere(String.valueOf(position.getLatitudeHemisphere().toChar()));
                gpsHbaseVo.setLongitude(String.valueOf(position.getLongitude()));
                gpsHbaseVo.setLongitudeHemisphere(String.valueOf(position.getLongitudeHemisphere().toChar()));

                gpsHbaseVo.setPosition(String.valueOf(gga.getFixQuality().toInt()));
                gpsHbaseVo.setSatellites(String.valueOf(gga.getSatelliteCount()));
                gpsHbaseVo.setDefinition(String.valueOf(gga.getHorizontalDOP()));
                gpsHbaseVo.setAntennaHeight(String.valueOf(gga.getAltitude()));
                gpsHbaseVo.setAntennaUnit(String.valueOf(gga.getAltitudeUnits().toChar()));
                gpsHbaseVo.setGeoidHeight(String.valueOf(gga.getGeoidalHeight()));
                gpsHbaseVo.setGeoidUnit(String.valueOf(gga.getGeoidalHeightUnits().toChar()));
//                gpsHbaseVo.setTimeLimit(String.valueOf(gga.getDgpsAge()));
            }
            break;
            //时间 日期 位置 速度
            case "RMC": {
                RMCSentence rmc = (RMCSentence) sentence;
                // 获取UTC时间
                String utcTime = rmc.getWholeUtcTime();
                Date date = DateUtils.parseDateTime(utcTime);
                gpsHbaseVo.setInitialTime(String.valueOf(DateUtils.addDateHour2Long(date, 8)));
            }
            break;
            //地面速度信息
            case "VTG": {
                VTGSentence vtg = (VTGSentence) sentence;

                gpsHbaseVo.setRightNorthCourse(String.valueOf(vtg.getTrueCourse()));
                gpsHbaseVo.setRightUnit(String.valueOf(VTGSentence.TRUE));
//                gpsHbaseVo.setMagneticNorthCourse(String.valueOf(vtg.getMagneticCourse()));
                gpsHbaseVo.setMagneticUnit(String.valueOf(VTGSentence.MAGNETIC));
                gpsHbaseVo.setGroundRateJ(String.valueOf(vtg.getSpeedKnots()));
                gpsHbaseVo.setRateJUnit(String.valueOf(VTGSentence.KNOT));
                gpsHbaseVo.setGroundRateKm(String.valueOf(vtg.getSpeedKmh()));
                gpsHbaseVo.setRateKmUnit(String.valueOf(VTGSentence.KMPH));
            }
            break;
            case "GNS": {
                //默认东八区
                GNSSentence gns = (GNSSentence) sentence;
                gpsHbaseVo.setGnsUtcTime(gns.getTime().toString());

                Position gnsPosition = gns.getPosition();

                gpsHbaseVo.setGnsLatitude(String.valueOf(gnsPosition.getLatitude()));
                gpsHbaseVo.setGnsLatitudeHemisphere(String.valueOf(gnsPosition.getLatitudeHemisphere().toChar()));
                gpsHbaseVo.setGnsLongitude(String.valueOf(gnsPosition.getLongitude()));
                gpsHbaseVo.setGnsLongitudeHemisphere(String.valueOf(gnsPosition.getLongitudeHemisphere().toChar()));

                gpsHbaseVo.setGnsDefinition(String.valueOf(gns.getHorizontalDOP()));
                gpsHbaseVo.setGnsAntennaHeight(String.valueOf(gns.getOrthometricHeight()));
                gpsHbaseVo.setGnsGeoidHeight(String.valueOf(gns.getGeoidalSeparation()));
            }
            break;
            case "GBS": {
                //默认东八区
                GBSSentence gbs = (GBSSentence) sentence;
                gpsHbaseVo.setLatError(String.valueOf(gbs.getLatitudeError()));
                gpsHbaseVo.setLongError(String.valueOf(gbs.getLongitudeError()));
                gpsHbaseVo.setAltError(String.valueOf(gbs.getAltitudeError()));
                gpsHbaseVo.setSatelliteId(String.valueOf(gbs.getSatelliteId()));
                gpsHbaseVo.setProbability(String.valueOf(gbs.getProbability()));
                gpsHbaseVo.setEstimate(String.valueOf(gbs.getEstimate()));
                gpsHbaseVo.setDeviation(String.valueOf(gbs.getDeviation()));
            }
            break;
            //时间
            case "ZDA": {
                //默认东八区
                ZDASentence zs = (ZDASentence) sentence;
                gpsHbaseVo.setDay(String.valueOf(zs.getDate().getDay()));
                gpsHbaseVo.setMonth(String.valueOf(zs.getDate().getMonth()));
                gpsHbaseVo.setYear(String.valueOf(zs.getDate().getYear()));
//                gpsHbaseVo.setLocalZoneHours(String.valueOf(zs.getLocalZoneHours()));
//                gpsHbaseVo.setLocalZoneMinutes(String.valueOf(zs.getLocalZoneMinutes()));
            }
            break;
            //经度 纬度 UTC时间
            case "GLL": {
                GLLSentence glls = (GLLSentence) sentence;
                Time time = glls.getTime();
            }
            break;
            case "GST": {
            }
            break;
            //船艏向-真实
            case "HDT": {
                HDTSentence hdt = (HDTSentence) sentence;
            }
            break;
            default: {
            }
        }
    }

    /**
     * 解析预览
     *
     * @param line
     * @return
     */
    private static void translateParseLine(GpsHbaseVo gpsHbaseVo, String line) {
        if (line.startsWith("@&")) {
            return;
        }
        SentenceFactory sf = SentenceFactory.getInstance();
        Sentence sentence = sf.createParser(line);

        switch (sentence.getSentenceId()) {
            //时间 位置 定位类型（全球地位信息）
            case "GGA": {
                GGASentence gga = (GGASentence) sentence;
                gpsHbaseVo.setUtcTime(gga.getTime().toString());

                Position position = gga.getPosition();

                gpsHbaseVo.setLatitude(String.valueOf(position.getLatitude()));
                gpsHbaseVo.setLatitudeHemisphere(String.valueOf(position.getLatitudeHemisphere().toChar()));
                gpsHbaseVo.setLongitude(String.valueOf(position.getLongitude()));
                gpsHbaseVo.setLongitudeHemisphere(String.valueOf(position.getLongitudeHemisphere().toChar()));

                gpsHbaseVo.setPosition(String.valueOf(gga.getFixQuality().toInt()));
                gpsHbaseVo.setSatellites(String.valueOf(gga.getSatelliteCount()));
                gpsHbaseVo.setDefinition(String.valueOf(gga.getHorizontalDOP()));
                gpsHbaseVo.setAntennaHeight(String.valueOf(gga.getAltitude()));
                gpsHbaseVo.setAntennaUnit(String.valueOf(gga.getAltitudeUnits().toChar()));
                gpsHbaseVo.setGeoidHeight(String.valueOf(gga.getGeoidalHeight()));
                gpsHbaseVo.setGeoidUnit(String.valueOf(gga.getGeoidalHeightUnits().toChar()));
//                gpsHbaseVo.setTimeLimit(String.valueOf(gga.getDgpsAge()));
            }
            break;
            //时间 日期 位置 速度
            case "RMC": {
                RMCSentence rmc = (RMCSentence) sentence;
                // 获取UTC时间
                String utcTime = rmc.getWholeUtcTime();
                Date date = DateUtils.parseDateTime(utcTime);

                gpsHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(DateUtils.addDateHour2Long(date, 8),"yyyy-MM-dd HH:mm:ss"));
            }
            break;
            //地面速度信息
            case "VTG": {
                VTGSentence vtg = (VTGSentence) sentence;

                gpsHbaseVo.setRightNorthCourse(String.valueOf(vtg.getTrueCourse()));
                gpsHbaseVo.setRightUnit(String.valueOf(VTGSentence.TRUE));
//                gpsHbaseVo.setMagneticNorthCourse(String.valueOf(vtg.getMagneticCourse()));
                gpsHbaseVo.setMagneticUnit(String.valueOf(VTGSentence.MAGNETIC));
                gpsHbaseVo.setGroundRateJ(String.valueOf(vtg.getSpeedKnots()));
                gpsHbaseVo.setRateJUnit(String.valueOf(VTGSentence.KNOT));
                gpsHbaseVo.setGroundRateKm(String.valueOf(vtg.getSpeedKmh()));
                gpsHbaseVo.setRateKmUnit(String.valueOf(VTGSentence.KMPH));
            }
            break;
            case "GNS": {
                //默认东八区
                GNSSentence gns = (GNSSentence) sentence;
                gpsHbaseVo.setGnsUtcTime(gns.getTime().toString());

                Position gnsPosition = gns.getPosition();

                gpsHbaseVo.setGnsLatitude(String.valueOf(gnsPosition.getLatitude()));
                gpsHbaseVo.setGnsLatitudeHemisphere(String.valueOf(gnsPosition.getLatitudeHemisphere().toChar()));
                gpsHbaseVo.setGnsLongitude(String.valueOf(gnsPosition.getLongitude()));
                gpsHbaseVo.setGnsLongitudeHemisphere(String.valueOf(gnsPosition.getLongitudeHemisphere().toChar()));

                gpsHbaseVo.setGnsDefinition(String.valueOf(gns.getHorizontalDOP()));
                gpsHbaseVo.setGnsAntennaHeight(String.valueOf(gns.getOrthometricHeight()));
                gpsHbaseVo.setGnsGeoidHeight(String.valueOf(gns.getGeoidalSeparation()));
            }
            break;
            case "GBS": {
                //默认东八区
                GBSSentence gbs = (GBSSentence) sentence;
                gpsHbaseVo.setLatError(String.valueOf(gbs.getLatitudeError()));
                gpsHbaseVo.setLongError(String.valueOf(gbs.getLongitudeError()));
                gpsHbaseVo.setAltError(String.valueOf(gbs.getAltitudeError()));
                gpsHbaseVo.setSatelliteId(String.valueOf(gbs.getSatelliteId()));
                gpsHbaseVo.setProbability(String.valueOf(gbs.getProbability()));
                gpsHbaseVo.setEstimate(String.valueOf(gbs.getEstimate()));
                gpsHbaseVo.setDeviation(String.valueOf(gbs.getDeviation()));
            }
            break;
            //时间
            case "ZDA": {
                //默认东八区
                ZDASentence zs = (ZDASentence) sentence;
                gpsHbaseVo.setDay(String.valueOf(zs.getDate().getDay()));
                gpsHbaseVo.setMonth(String.valueOf(zs.getDate().getMonth()));
                gpsHbaseVo.setYear(String.valueOf(zs.getDate().getYear()));
//                gpsHbaseVo.setLocalZoneHours(String.valueOf(zs.getLocalZoneHours()));
//                gpsHbaseVo.setLocalZoneMinutes(String.valueOf(zs.getLocalZoneMinutes()));
            }
            break;
            //经度 纬度 UTC时间
            case "GLL": {
                GLLSentence glls = (GLLSentence) sentence;
                Time time = glls.getTime();
            }
            break;
            case "GST": {
            }
            break;
            //船艏向-真实
            case "HDT": {
                HDTSentence hdt = (HDTSentence) sentence;
            }
            break;
            default: {
            }
        }
    }

}
