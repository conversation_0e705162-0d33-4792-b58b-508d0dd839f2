package com.snct.analysis;

import com.snct.kafka.KafkaMessage;
import com.snct.analysis.vo.CompassHbaseVo;
import com.snct.common.utils.DateUtils;
import com.snct.analysis.domain.compass.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 电罗经
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class CompassAnalysis {
    protected static Logger logger = LoggerFactory.getLogger(CompassAnalysis.class);

    private static final String TEMP_KEY = "COMPASS_TEMP";

    /**
     * 从一组aws数据中，取得有用的字段
     * 判断数据是否同一秒，同一秒则只取其中一组
     *
     * @param kafkaMessage
     * @return
     */
    public synchronized static CompassHbaseVo getCompassList(KafkaMessage kafkaMessage) {
        CompassHbaseVo compassHbaseVo = null;
        List<String> temp = BaseAnalysis.getTempList(TEMP_KEY, kafkaMessage.getCode());
        if (temp == null) {
            temp = new ArrayList<>();
        }

        try {
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime();
            // 判断时间是否为同一秒
            boolean isSame = BaseAnalysis.isSameTime(TEMP_KEY, kafkaMessage.getCode(), currentTime);

            // 同一秒的数据不做发送，在下一秒后再一起合并
            if (!CollectionUtils.isEmpty(temp) && !isSame) {
                compassHbaseVo = new CompassHbaseVo();
                compassHbaseVo.setInitialTime(DateUtils.fetchWholeSecond(currentTime).toString());
                int sizeNum = 1;
                logger.info("循环几次---{}",temp.size());
                for (String line : temp) {
                    logger.info("循环到第几次了---{}",sizeNum);
                    translateLine(compassHbaseVo, line);
                    if (sizeNum == temp.size() && temp.size()>1){
                        break;
                    }
//                    if (!StringUtils.isEmpty(compassHbaseVo.getHehdt())) {
//                       break;
//                    }
                    sizeNum++;
                }
                if (temp.size()>1){
                    temp.clear();
                }
            }

            temp.add(kafkaMessage.getMsg());
            BaseAnalysis.updateTemp(TEMP_KEY, kafkaMessage.getCode(), temp, currentTime);
        } catch (Exception e) {
            logger.error("Compass解析出错,---{}", e);
            temp.clear();
        }
        return compassHbaseVo;
    }

    /**
     * 解析预览-数据解析单独处理
     *
     * @param kafkaMessage
     * @return
     */
    public synchronized static CompassHbaseVo getParseCompassList(List<KafkaMessage> kafkaMessage) {
        CompassHbaseVo compassHbaseVo = null;
//        CompassVo compassVo = null;
//        List<Hproperty> hpropertys = null;
//        Map<String,String> map = new HashMap<>();
        List<String> temp = new ArrayList<>();
        for (KafkaMessage kafkaMessage1:
                kafkaMessage) {
            temp.add(kafkaMessage1.getMsg());
        }
//        List<String> temp = kafkaMessage.stream().map(kafkaMessage1 -> {return kafkaMessage1.getMsg();}).collect(Collectors.toList());
//        List<String> temp = BaseAnalysis.getTempList(TEMP_KEY, kafkaMessage.getCode());
//        if (temp == null) {
//            temp = new ArrayList<>();
//        }
//
//        try {
//            long currentTime = kafkaMessage.get(0).getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.get(0).getInitialTime();
////
////            // 判断时间是否为同一秒
////            boolean isSame = BaseAnalysis.isSameTime(TEMP_KEY, kafkaMessage.getCode(), currentTime);
//
//             if (!CollectionUtils.isEmpty(temp)) {
//                 compassVo = new CompassVo();
                compassHbaseVo = new CompassHbaseVo();
//                 hpropertys = new ArrayList<Hproperty>();
//                compassHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(currentTime,"yyyy-MM-dd HH:mm:ss").toString());
//                 compassVo.setCompassHbaseVo(compassHbaseVo);
//                 compassVo.setHproperty(hpropertys);
                for (String line : temp) {
                    logger.info("line解析预览:"+line);
                    translateLine(compassHbaseVo, line);
//                    if (!StringUtils.isEmpty(compassHbaseVo.getHehdt())) {
//                        break;
//                    }

                }
                temp.clear();
        if (AwsAnalysis.checkObjAllFieldsIsNull(compassHbaseVo)){
            compassHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss").toString());
            compassHbaseVo.setInitialTime(null);
        }else{
            return null;
        }
//
//
//                 exchange(compassVo);//属性添加
//            }
//
//            temp.add(kafkaMessage.getMsg());
//
//            BaseAnalysis.updateTemp(TEMP_KEY, kafkaMessage.getCode(), temp, currentTime);
//        } catch (Exception e) {
//            logger.error("Compass解析出错,---{}", e);
//            temp.clear();
//        }
        return compassHbaseVo;
    }

    /**
     * 转换一行数据
     *
     * @param line
     * @return
     */
    private static void translateLine(CompassHbaseVo compassHbaseVo, String line) {
        String prefix = line.substring(0, 6);
        switch (prefix) {
            //（全球地位信息）
            case "$HEHDT": {
                Hehdt hehdt = new Hehdt();
                hehdt.dataAnalysis(line);
                compassHbaseVo.setHehdt(hehdt.getCompassData());
            }
            break;
            case "$HCHDM": {
                Hchdm hchdm = new Hchdm();
                hchdm.dataAnalysis(line);
                compassHbaseVo.setHehdt(hchdm.getCompassData());
            }
            break;
            case "$HEROT": {
                Herot herot = new Herot();
                herot.dataAnalysis(line);
                compassHbaseVo.setHerot(herot.getTurningRate());
            }
            break;

            default: {
            }
        }
    }

    /**
     * 转换一行数据
     *
     * @param line
     * @return
     */
    private static void translateParseLine(CompassVo compassHbaseVo, String line) {

        String prefix = line.substring(0, 6);
        switch (prefix) {
            //（全球地位信息）
            case "$HEHDT": {
                Hehdt hehdt = new Hehdt();
                hehdt.dataAnalysis(line);
                compassHbaseVo.getCompassHbaseVo().setHehdt(hehdt.getCompassData());
            }
            break;
            case "$HCHDM": {
                Hchdm hchdm = new Hchdm();
                hchdm.dataAnalysis(line);
                compassHbaseVo.getCompassHbaseVo().setHehdt(hchdm.getCompassData());
            }
            break;
            case "$HEROT": {
                Herot herot = new Herot();
                herot.dataAnalysis(line);
                compassHbaseVo.getCompassHbaseVo().setHerot(herot.getTurningRate());
            }
            break;

            default: {
            }
        }
    }

//    //获取属性上注解名称
//    private static void exchange(CompassVo compassVo){
//        logger.error(compassVo.getCompassHbaseVo()+"3333333");
//        CompassHbaseVo compassHbaseVo = compassVo.getCompassHbaseVo();
//        Field[] fields = compassHbaseVo.getClass().getDeclaredFields();
//        logger.error(fields+"222222222");
//        for (Field field : fields) {
//            try {
//                if (!field.isAccessible()) {
//                    field.setAccessible(true);
//                }
//                if (field.get(compassHbaseVo)!=null){
//                    //不为空的才加进去属性
//                    Hproperty hproperty = new Hproperty();
//                    Excel excel = field.getAnnotation(Excel.class);
//                    hproperty.setLabel(excel.name());
//                    hproperty.setProperty(field.getName());
//                    compassVo.getHproperty().add(hproperty);
//                }
//
//            } catch (IllegalAccessException e) {
//                logger.error("compass反射解析预览错误");
//            }
//
//        }
//    }


}
