package com.snct.snmp;

import java.util.Iterator;
import org.snmp4j.CommunityTarget;
import org.snmp4j.PDU;
import org.snmp4j.Snmp;
import org.snmp4j.event.ResponseEvent;
import org.snmp4j.smi.Address;
import org.snmp4j.smi.GenericAddress;
import org.snmp4j.smi.OID;
import org.snmp4j.smi.OctetString;
import org.snmp4j.smi.VariableBinding;
import org.snmp4j.transport.DefaultUdpTransportMapping;

/**
 * SNMP（Simple Network Management Protocol） 是一种网络设备的远程监控和管理协议，可以用来 查询设备状态、配置参数、接收告警信息
 */
public class SNMPExample {
    public static void main(String[] args) {
        try {
            // 设置目标设备地址和端口
            Address targetHost = GenericAddress.parse("udp:***************/161");

            // 配置 SNMP 目标参数
            CommunityTarget target = new CommunityTarget();
            target.setCommunity(new OctetString("public")); // 社区名
            target.setVersion(1); // SNMP v1
            target.setAddress(targetHost);
            target.setRetries(2);      // 重试次数
            target.setTimeout(1500L);  // 超时时间（ms）

            // 初始化 SNMP 实例并启动
            Snmp snmp = new Snmp(new DefaultUdpTransportMapping());
            snmp.listen();

            // 构建 PDU，请求的 OID
            PDU pdu = new PDU();
            pdu.add(new VariableBinding(new OID(".*******.*******.0"))); // 系统描述
            pdu.add(new VariableBinding(new OID(".*******.*******.0"))); // 系统启动时间
            pdu.add(new VariableBinding(new OID(".*******.4.1.50001.1.1.0"))); // 设备IP地址
            pdu.add(new VariableBinding(new OID(".*******.4.1.50001.1.3.0"))); // 设备序列号
            pdu.add(new VariableBinding(new OID(".*******.4.1.50001.1.201.0"))); // 设备运行频率
            pdu.setType(PDU.GET);

            // 发送请求并获取响应
            ResponseEvent responseEvent = snmp.send(pdu, target);
            PDU response = responseEvent.getResponse();

            if (response != null) {
                if (response.getErrorStatus() == 0) {
                    System.out.println("SNMP GET successful!");
                    Iterator<?> it = response.getVariableBindings().iterator();
                    while (it.hasNext()) {
                        VariableBinding vb = (VariableBinding) it.next();
                        System.out.println(vb.getOid() + " : " + vb.getVariable());
                    }
                } else {
                    System.out.println("SNMP GET failed.");
                    System.out.println("Error: " + response.getErrorStatusText());
                }
            } else {
                System.out.println("No response received.");
            }

            snmp.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
