package com.snct.snmp;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.snmp4j.PDU;
import org.snmp4j.smi.OID;
import org.snmp4j.smi.VariableBinding;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SNMP请求封装类
 * 用于构建SNMP请求PDU
 * 
 * <AUTHOR>
 */
public class SnmpRequest {
    
    private static final Logger logger = LoggerFactory.getLogger(SnmpRequest.class);
    
    // 请求OID列表
    private final List<String> oidList = new ArrayList<>();
    
    // OID描述映射
    private final Map<String, String> oidDescriptions = new HashMap<>();
    
    // 请求类型
    private int requestType = PDU.GET;
    
    /**
     * 创建空的SNMP请求
     */
    public SnmpRequest() {
    }
    
    /**
     * 创建带OID的SNMP请求
     * 
     * @param oids 需要查询的OID数组
     */
    public SnmpRequest(String... oids) {
        for (String oid : oids) {
            addOid(oid);
        }
    }
    
    /**
     * 添加OID到请求中
     * 
     * @param oid OID字符串
     * @return 当前请求对象
     */
    public SnmpRequest addOid(String oid) {
        // 如果OID不以.开头，添加.前缀
        if (!oid.startsWith(".")) {
            oid = "." + oid;
        }
        
        if (!oidList.contains(oid)) {
            oidList.add(oid);
        }
        return this;
    }
    
    /**
     * 添加带描述的OID到请求中
     * 
     * @param oid OID字符串
     * @param description OID的描述
     * @return 当前请求对象
     */
    public SnmpRequest addOid(String oid, String description) {
        // 如果OID不以.开头，添加.前缀
        if (!oid.startsWith(".")) {
            oid = "." + oid;
        }
        
        if (!oidList.contains(oid)) {
            oidList.add(oid);
            oidDescriptions.put(oid, description);
        }
        return this;
    }
    
    /**
     * 设置请求类型
     * 
     * @param requestType 请求类型，如 PDU.GET, PDU.GETNEXT 等
     * @return 当前请求对象
     */
    public SnmpRequest setRequestType(int requestType) {
        this.requestType = requestType;
        return this;
    }
    
    /**
     * 获取OID列表
     * 
     * @return OID列表
     */
    public List<String> getOidList() {
        return new ArrayList<>(oidList);
    }
    
    /**
     * 获取OID描述
     * 
     * @param oid OID字符串
     * @return OID的描述，如果没有则返回OID本身
     */
    public String getOidDescription(String oid) {
        return oidDescriptions.getOrDefault(oid, oid);
    }
    
    /**
     * 创建SNMP PDU
     * 
     * @return SNMP PDU对象
     */
    public PDU createPdu() {
        PDU pdu = new PDU();
        pdu.setType(requestType);
        
        for (String oid : oidList) {
            pdu.add(new VariableBinding(new OID(oid)));
        }
        
        return pdu;
    }
    
    /**
     * 是否有OID
     * 
     * @return 是否包含OID
     */
    public boolean hasOids() {
        return !oidList.isEmpty();
    }
    
    /**
     * 获取OID数量
     * 
     * @return OID数量
     */
    public int getOidCount() {
        return oidList.size();
    }
    
    @Override
    public String toString() {
        return "SnmpRequest{" +
                "oidCount=" + oidList.size() +
                ", requestType=" + requestType +
                '}';
    }
} 