package com.snct.web.controller.system;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.enums.BusinessType;
import com.snct.system.domain.SysRestart;
import com.snct.system.service.ISysSysRestartService;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.common.core.page.TableDataInfo;

/**
 * 任务Controller
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
@RestController
@RequestMapping("/system/restart")
public class SysRestartController extends BaseController
{
    @Autowired
    private ISysSysRestartService sysRestartService;

    /**
     * 查询任务列表
     */
    @PreAuthorize("@ss.hasPermi('system:restart:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysRestart sysrestart)
    {
        startPage();
        List<SysRestart> list = sysRestartService.selectSysRestartList(sysrestart);
        return getDataTable(list);
    }

    /**
     * 导出任务列表
     */
    @PreAuthorize("@ss.hasPermi('system:restart:export')")
    @Log(title = "任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysRestart sysrestart)
    {
        List<SysRestart> list = sysRestartService.selectSysRestartList(sysrestart);
        ExcelUtil<SysRestart> util = new ExcelUtil<SysRestart>(SysRestart.class);
        util.exportExcel(response, list, "任务数据");
    }

    /**
     * 获取任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:restart:query')")
    @GetMapping(value = "/{restartId}")
    public AjaxResult getInfo(@PathVariable("restartId") Long restartId)
    {
        return success(sysRestartService.selectSysRestartByRestartId(restartId));
    }

    /**
     * 新增任务
     */
    @PreAuthorize("@ss.hasPermi('system:restart:add')")
    @Log(title = "任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysRestart sysrestart)
    {
        if(sysrestart.getRestartType()!=1){
            if(sysrestart.getToday()==null){
                return error("日期不能为空");
            }
        }

        if (!sysRestartService.checkRestartNameUnique(sysrestart))
        {
            return error("新增任务'" + sysrestart.getRestartName() + "'失败，名称已存在");
        }
//        else if (!sysrestartService.checkrestartCodeUnique(sysrestart))
//        {
//            return error("新增岗位'" + sysrestart.getrestartCode() + "'失败，编码已存在");
//        }
        sysrestart.setDoTime(new Date( System.currentTimeMillis() - 59000000 ));
        sysrestart.setCreateBy(getUsername());
        return toAjax(sysRestartService.insertSysRestart(sysrestart));
    }

    /**
     * 修改任务
     */
    @PreAuthorize("@ss.hasPermi('system:restart:edit')")
    @Log(title = "任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysRestart sysrestart)
    {
        sysrestart.setDoTime(new Date( System.currentTimeMillis() - 59000000 ));
        return toAjax(sysRestartService.updateSysRestart(sysrestart));
    }

    /**
     * 删除任务
     */
    @PreAuthorize("@ss.hasPermi('system:restart:remove')")
    @Log(title = "任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{restartIds}")
    public AjaxResult remove(@PathVariable Long[] restartIds)
    {
        return toAjax(sysRestartService.deleteSysRestartByRestartIds(restartIds));
    }
}
