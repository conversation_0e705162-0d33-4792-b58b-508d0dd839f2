package com.snct.netty;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.bytes.ByteArrayDecoder;
import io.netty.handler.codec.bytes.ByteArrayEncoder;
import io.netty.handler.timeout.IdleStateHandler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Netty TCP客户端
 */
public class NettyClient {

    private static final Logger logger = LoggerFactory.getLogger(NettyClient.class);

    // 设备主机地址
    private final String host;
    
    // 设备端口
    private final int port;
    
    // 设备标识
    private final String deviceId;
    
    // Netty事件循环组
    private EventLoopGroup group;
    
    // Netty通道
    private Channel channel;
    
    // 设备处理器
    private final DeviceHandler deviceHandler;
    
    // 是否已连接标志
    private final AtomicBoolean connected = new AtomicBoolean(false);
    
    /**
     * 构造Netty客户端
     * 
     * @param host 设备主机地址
     * @param port 设备端口
     * @param deviceId 设备标识
     * @param deviceHandler 设备处理器
     */
    public NettyClient(String host, int port, String deviceId, DeviceHandler deviceHandler) {
        this.host = host;
        this.port = port;
        this.deviceId = deviceId;
        this.deviceHandler = deviceHandler;
    }
    
    /**
     * 建立与设备的连接
     * 
     * @return 是否成功连接
     */
    public boolean connect() {
        if (connected.get()) {
            logger.info("设备[{}]已连接: {}:{}", deviceId, host, port);
            return true;
        }
        
        try {
            // 创建Netty事件循环组
            group = new NioEventLoopGroup();
            
            // 创建引导程序
            Bootstrap bootstrap = new Bootstrap();
            bootstrap.group(group)
                    .channel(NioSocketChannel.class)
                    .option(ChannelOption.TCP_NODELAY, true)
                    .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 5000)
                    .handler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        protected void initChannel(SocketChannel ch) throws Exception {
                            ch.pipeline().addLast(
                                    // 空闲状态处理器，用于检测连接是否活跃
                                    new IdleStateHandler(0, 0, 60, TimeUnit.SECONDS),
                                    // 字节数组编码器
                                    new ByteArrayEncoder(),
                                    // 字节数组解码器
                                    new ByteArrayDecoder(),
                                    // 设备消息处理器
                                    deviceHandler
                            );
                        }
                    });
            
            // 连接到设备
            ChannelFuture future = bootstrap.connect(host, port).sync();
            if (future.isSuccess()) {
                channel = future.channel();
                connected.set(true);
                logger.info("设备[{}]连接成功: {}:{}", deviceId, host, port);
                
                // 添加连接关闭监听器
                channel.closeFuture().addListener(f -> {
                    connected.set(false);
                    logger.info("设备[{}]连接已关闭: {}:{}", deviceId, host, port);
                });
                
                return true;
            } else {
                logger.error("设备[{}]连接失败: {}:{}", deviceId, host, port);
                return false;
            }
        } catch (Exception e) {
            logger.error("设备[{}]连接异常: {}:{}", deviceId, host, port, e);
            shutdown();
            return false;
        }
    }
    
    /**
     * 断开与设备的连接
     */
    public void disconnect() {
        if (channel != null && channel.isOpen()) {
            try {
                channel.close().sync();
                logger.info("设备[{}]连接已断开: {}:{}", deviceId, host, port);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("设备[{}]断开连接中断: {}:{}", deviceId, host, port, e);
            }
        }
        connected.set(false);
    }
    
    /**
     * 关闭Netty客户端资源
     */
    public void shutdown() {
        disconnect();
        if (group != null && !group.isShutdown()) {
            try {
                group.shutdownGracefully().sync();
                logger.info("设备[{}]客户端已关闭", deviceId);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("设备[{}]客户端关闭中断", deviceId, e);
            }
        }
    }
    
    /**
     * 发送字节消息到设备
     * 
     * @param bytes 字节数组
     * @return 是否发送成功
     */
    public boolean sendMessage(byte[] bytes) {
        if (!connected.get() || channel == null || !channel.isActive()) {
            logger.error("设备[{}]未连接，无法发送消息", deviceId);
            return false;
        }
        
        try {
            // 发送消息
            channel.writeAndFlush(bytes);
            return true;
        } catch (Exception e) {
            logger.error("设备[{}]发送消息异常", deviceId, e);
            return false;
        }
    }
    
    /**
     * 检查是否已连接到设备
     * 
     * @return 是否已连接
     */
    public boolean isConnected() {
        return connected.get() && channel != null && channel.isActive();
    }
    
    /**
     * 获取设备主机地址
     * 
     * @return 主机地址
     */
    public String getHost() {
        return host;
    }
    
    /**
     * 获取设备端口
     * 
     * @return 端口
     */
    public int getPort() {
        return port;
    }
    
    /**
     * 获取设备标识
     * 
     * @return 设备标识
     */
    public String getDeviceId() {
        return deviceId;
    }
} 