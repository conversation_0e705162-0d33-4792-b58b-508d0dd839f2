package com.snct.analysis.domain.racdu;


import com.snct.system.domain.msg.Instrument;

/**
 * @description: Racdu1与Racdu2仪器不同
 * Example:$RIRSA,01.1,A,,*04
 * $RIRSA,<1>,A,,*CS<CR><LF>
 * 逗号隔开
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Rac<PERSON> extends Instrument {
    /**
     * 1.自动舵转向率
     */
    private String autopilotRate;
    /**
     * 2.状态位 A=数据有效
     */
    private String status;

    @Override
    public void dataAnalysis(String dataStr) {
        String[] values = dataStr.split(",", -1);
        values = super.valuesTrim(values);
        autopilotRate = values[1];
        status = values[2];
    }

    public String getAutopilotRate() {
        return autopilotRate;
    }

    public void setAutopilotRate(String autopilotRate) {
        this.autopilotRate = autopilotRate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
