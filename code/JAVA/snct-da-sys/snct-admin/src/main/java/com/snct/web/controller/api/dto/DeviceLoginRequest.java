package com.snct.web.controller.api.dto;

import javax.validation.constraints.NotBlank;

/**
 * 设备登录请求DTO
 * 
 * <AUTHOR>
 */
public class DeviceLoginRequest
{
    /**
     * 设备账号
     */
    @NotBlank(message = "设备账号不能为空")
    private String deviceAccount;

    /**
     * 设备密码
     */
    @NotBlank(message = "设备密码不能为空")
    private String devicePassword;

    public String getDeviceAccount()
    {
        return deviceAccount;
    }

    public void setDeviceAccount(String deviceAccount)
    {
        this.deviceAccount = deviceAccount;
    }

    public String getDevicePassword()
    {
        return devicePassword;
    }

    public void setDevicePassword(String devicePassword)
    {
        this.devicePassword = devicePassword;
    }

    @Override
    public String toString()
    {
        return "DeviceLoginRequest{" +
                "deviceAccount='" + deviceAccount + '\'' +
                ", devicePassword='[PROTECTED]'" +
                '}';
    }
}
