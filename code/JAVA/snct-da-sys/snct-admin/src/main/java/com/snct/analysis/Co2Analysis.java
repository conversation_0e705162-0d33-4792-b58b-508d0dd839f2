package com.snct.analysis;

import com.snct.kafka.KafkaMessage;
import com.snct.analysis.vo.Co2HbaseVo;
import com.snct.common.utils.DateUtils;
import com.snct.analysis.domain.co2.Equ;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * @description:
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Co2Analysis {
    protected static Logger logger = LoggerFactory.getLogger(Co2Analysis.class);

    /**
     * 从一组aws数据中，取得有用的字段
     *
     * @param kafkaMessage
     * @return
     */
    public static Co2HbaseVo getCo2List(KafkaMessage kafkaMessage) {
        Co2HbaseVo co2HbaseVo = null;
        try {
            if (kafkaMessage.getMsg().startsWith("EQU") || kafkaMessage.getMsg().startsWith("ATM")) {
                co2HbaseVo = new Co2HbaseVo();
                long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime();
                co2HbaseVo.setInitialTime(DateUtils.fetchWholeSecond(currentTime).toString());
                // 转换一个完整aws数据
                translateLine(co2HbaseVo, kafkaMessage);
            }
        } catch (Exception e) {
            logger.error("co2解析出错,---{}", e);
        }
        return co2HbaseVo;
    }

    /**
     * 解析预览
     *
     * @param kafkaMessage
     * @return
     */
    public static Co2HbaseVo getParseCo2List(List<KafkaMessage> kafkaMessage) {
        Co2HbaseVo co2HbaseVo = null;
        try {
            for (KafkaMessage kafkaMessage1:
                    kafkaMessage) {
                if (kafkaMessage1.getMsg().startsWith("EQU") || kafkaMessage1.getMsg().startsWith("ATM")) {
                    co2HbaseVo = new Co2HbaseVo();
                     // 转换一个完整aws数据
                    translateLine(co2HbaseVo, kafkaMessage1);
                }
            }
            if (AwsAnalysis.checkObjAllFieldsIsNull(co2HbaseVo)){
                co2HbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss").toString());
                co2HbaseVo.setInitialTime(null);
            }else{
                return null;
            }
        } catch (Exception e) {
            logger.error("co2解析出错,---{}", e);
        }
        return co2HbaseVo;
    }


    /**
     * 转换一行数据
     *
     * @param kafkaMessage
     * @return
     */
    private static void translateLine(Co2HbaseVo co2HbaseVo, KafkaMessage kafkaMessage) {

        String prefix = kafkaMessage.getMsg().substring(0, 3);
        if ("EQU".equals(prefix) || "ATM".equals(prefix)) {
            Equ equ = new Equ();
            equ.dataAnalysis(kafkaMessage.getMsg());

            BeanUtils.copyProperties(equ, co2HbaseVo);
        }
    }

}
