package com.snct.analysis.vo;

import com.snct.common.annotation.Excel;

public class AttitudeHbaseVo {

    private String id;

    private String initialTime;

    @Excel(
        name = "录入时间"
    )
    private String initialBjTime;

    @Excel(
        name = "横摇"
    )
    private String roll;

    @Excel(
        name = "纵摇"
    )
    private String pitch;

    @Excel(
        name = "升沉"
    )
    private String heave;

    @Excel(
        name = "航向"
    )
    private String heading;

    @Excel(
        name = "utc时间"
    )
    private String utcTime;

    @Excel(
        name = "名称"
    )
    private String stationName;

    public AttitudeHbaseVo() {
    }

    public String getStationName() {
        return this.stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRoll() {
        return this.roll;
    }

    public void setRoll(String roll) {
        this.roll = roll;
    }

    public String getPitch() {
        return this.pitch;
    }

    public void setPitch(String pitch) {
        this.pitch = pitch;
    }

    public String getHeave() {
        return this.heave;
    }

    public void setHeave(String heave) {
        this.heave = heave;
    }

    public String getHeading() {
        return this.heading;
    }

    public void setHeading(String heading) {
        this.heading = heading;
    }

    public String getInitialTime() {
        return this.initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return this.initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getUtcTime() {
        return this.utcTime;
    }

    public void setUtcTime(String utcTime) {
        this.utcTime = utcTime;
    }
}
