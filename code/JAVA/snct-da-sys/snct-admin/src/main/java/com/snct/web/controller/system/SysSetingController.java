package com.snct.web.controller.system;

import com.alibaba.fastjson2.JSONObject;
import com.snct.common.annotation.Log;
import com.snct.common.config.SnctConfig;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.core.page.TableDataInfo;
import com.snct.common.enums.BusinessType;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.system.domain.SysConfig;
import com.snct.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.InetAddress;
import java.util.List;

/**
 * 参数配置 信息操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/seting")
public class SysSetingController extends BaseController
{
    @Autowired
    private ISysConfigService configService;

    /**
     * 获取参数配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:seting:info')")
    @GetMapping("/info")
    public AjaxResult info()
    {
        SysConfig config = new SysConfig();
        config.setCreateBy("sys");
        List<SysConfig> list = configService.selectConfigList(config);
        JSONObject obj = new JSONObject();
        for(SysConfig conf : list){
            obj.put(conf.getConfigKey(), conf.getConfigValue());
        }
        return AjaxResult.success(obj);
    }

    /**
     * 修改参数配置
     */
    //@PreAuthorize("@ss.hasPermi('system:seting:edit')")
    @Log(title = "参数管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody JSONObject json)
    {
        String bd = json.get("bdselectedOption")+"";
        String net =  json.get("netselectedOption")+"";
        if("Y".equals(bd)){
            //检查北斗连接信号
        }
        if("Y".equals(net)){
            //检查kafka服务网络
            try {
                // 尝试连接到一个公共IP地址，超时时间为1秒
                InetAddress.getByName(SnctConfig.getKafkaServerTrl().split(":")[0]).isReachable(1000);
            } catch (IOException e) {
                // 发生异常则代表网络不可用
                return AjaxResult.error("请检查消息服务网络是否连接");
            }
        }
        SysConfig sysConfigbd = configService.selectConfigByConfigKeyKey("bd_transfer_function");
        sysConfigbd.setConfigValue(bd);
        configService.updateConfig(sysConfigbd);
        SysConfig sysConfignet = configService.selectConfigByConfigKeyKey("net_transfer_function");
        sysConfignet.setConfigValue(net);
        configService.updateConfig(sysConfignet);
        return AjaxResult.success();
    }

}
