package com.snct.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.snct.system.domain.msg.BuMsgPdu;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.enums.BusinessType;
import com.snct.system.service.IBuMsgPduService;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.common.core.page.TableDataInfo;

/**
 * pdu消息Controller
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
@RestController
@RequestMapping("/system/pdu")
public class BuMsgPduController extends BaseController
{
    @Autowired
    private IBuMsgPduService buMsgPduService;

    /**
     * 查询pdu消息列表
     */
    @PreAuthorize("@ss.hasPermi('system:pdu:list')")
    @GetMapping("/list")
    public TableDataInfo list(BuMsgPdu buMsgPdu)
    {
        startPage();
        List<BuMsgPdu> list = buMsgPduService.selectBuMsgPduList(buMsgPdu);
        return getDataTable(list);
    }

    /**
     * 导出pdu消息列表
     */
    @PreAuthorize("@ss.hasPermi('system:pdu:export')")
    @Log(title = "pdu消息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BuMsgPdu buMsgPdu)
    {
        List<BuMsgPdu> list = buMsgPduService.selectBuMsgPduList(buMsgPdu);
        ExcelUtil<BuMsgPdu> util = new ExcelUtil<BuMsgPdu>(BuMsgPdu.class);
        util.exportExcel(response, list, "pdu消息数据");
    }

    /**
     * 获取pdu消息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:pdu:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(buMsgPduService.selectBuMsgPduById(id));
    }

    /**
     * 新增pdu消息
     */
    @PreAuthorize("@ss.hasPermi('system:pdu:add')")
    @Log(title = "pdu消息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BuMsgPdu buMsgPdu)
    {
        return toAjax(buMsgPduService.insertBuMsgPdu(buMsgPdu));
    }

    /**
     * 修改pdu消息
     */
    @PreAuthorize("@ss.hasPermi('system:pdu:edit')")
    @Log(title = "pdu消息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BuMsgPdu buMsgPdu)
    {
        return toAjax(buMsgPduService.updateBuMsgPdu(buMsgPdu));
    }

    /**
     * 删除pdu消息
     */
    @PreAuthorize("@ss.hasPermi('system:pdu:remove')")
    @Log(title = "pdu消息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(buMsgPduService.deleteBuMsgPduByIds(ids));
    }
}
