package com.snct.task;

import com.snct.device.service.AmplifierDeviceService;
import com.snct.device.service.ModemDeviceService;
import com.snct.device.service.PduDeviceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 设备通信任务
 * 用于定时查询设备状态和数据采集
 *
 * <AUTHOR>
 */
@Component("deviceTask")
public class DeviceTask {

    private static final Logger logger = LoggerFactory.getLogger(DeviceTask.class);

    @Autowired
    private PduDeviceService pduDeviceService;

    @Autowired
    private ModemDeviceService modemDeviceService;

    @Autowired
    private AmplifierDeviceService amplifierDeviceService;

    /**
     * PDU设备查询任务
     * 发送查询命令获取PDU设备数据
     */
    public void queryPduData() {
        try {
            logger.info(">>> >>> >>> 执行PDU数据查询任务 >>> >>> >>>");
            pduDeviceService.sendAllQueryCommands();
        } catch (Exception e) {
            logger.error("PDU设备查询任务异常", e);
        }
    }

    /**
     * Modem设备查询任务
     * 通过SNMP协议获取Modem设备数据
     */
    public void queryModemData() {
        try {
            logger.info(">>> >>> >>> 执行Modem数据查询任务 >>> >>> >>>");
            modemDeviceService.queryAllModemData();
        } catch (Exception e) {
            logger.error("Modem设备查询任务异常", e);
        }
    }

    /**
     * 功放设备查询任务
     * 向功放设备发送查询命令获取状态
     */
    public void queryAmplifierData() {
        try {
            logger.info(">>> >>> >>> 执行功放数据查询任务 >>> >>> >>>");
            amplifierDeviceService.queryAllAmplifierData();
        } catch (Exception e) {
            logger.error("功放设备查询任务异常", e);
        }
    }

    /**
     * 设备数据综合查询任务
     * 同时查询所有类型的设备数据
     */
    public void queryAllDeviceData() {

        try {
            // 查询PDU设备
            pduDeviceService.sendAllQueryCommands();

            // 查询Modem设备
            modemDeviceService.queryAllModemData();

            // 查询功放设备
            amplifierDeviceService.queryAllAmplifierData();

        } catch (Exception e) {
            logger.error("综合设备查询任务异常", e);
        }
    }
} 