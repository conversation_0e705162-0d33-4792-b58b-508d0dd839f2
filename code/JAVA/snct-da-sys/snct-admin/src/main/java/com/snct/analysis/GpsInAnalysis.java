package com.snct.analysis;

import com.snct.kafka.KafkaMessage;
import com.snct.analysis.vo.GpsInHbaseVo;
import com.snct.common.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * @description: Attitude  一秒多组
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class GpsInAnalysis {
    protected static Logger logger = LoggerFactory.getLogger(GpsInAnalysis.class);


    /**
     *
     *
     * @param kafkaMessage
     * @return
     */
    public synchronized static GpsInHbaseVo getGpsNewList(KafkaMessage kafkaMessage) {

        GpsInHbaseVo gpsNewHbaseVo = null;

        try {
            //当前时间
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime();
            if (StringUtils.isNotBlank(kafkaMessage.getMsg()) && kafkaMessage.getMsg().length()>=154){
                if (kafkaMessage.getMsg().substring(0,4).equalsIgnoreCase("5550")){
                    gpsNewHbaseVo = new GpsInHbaseVo();
                    gpsNewHbaseVo.setInitialTime(DateUtils.fetchWholeSecond(currentTime).toString());
                    List<String> gpars = new ArrayList<>();
                    for (int i = 0; i < kafkaMessage.getMsg().length(); i=i+2) {
                        gpars.add(kafkaMessage.getMsg().substring(i,i+2));
                    }
                    gpsNewHbaseVo = getGpsMsg(gpars,gpsNewHbaseVo);
                }else{

                }
            }


        } catch (Exception e) {
            logger.error("解析出错,---{}", e);
        }

        return gpsNewHbaseVo;
    }

    /**
     *
     *
     * @param kafkaMessage
     * @return
     */
    public synchronized static GpsInHbaseVo getGpsNewParseList(KafkaMessage kafkaMessage) {

        GpsInHbaseVo gpsNewHbaseVo = null;

        try {
            //当前时间
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime();
            if (StringUtils.isNotBlank(kafkaMessage.getMsg()) && kafkaMessage.getMsg().length()>=154){
                if (kafkaMessage.getMsg().substring(0,4).equalsIgnoreCase("5550")){
                    gpsNewHbaseVo = new GpsInHbaseVo();


                    List<String> gpars = new ArrayList<>();
                    for (int i = 0; i < kafkaMessage.getMsg().length(); i=i+2) {
                        gpars.add(kafkaMessage.getMsg().substring(i,i+2));
                    }
                    gpsNewHbaseVo = getGpsMsg(gpars,gpsNewHbaseVo);
                }else{

                }
            }


        } catch (Exception e) {
            logger.error("解析出错,---{}", e);
        }
        gpsNewHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss").toString());
        return gpsNewHbaseVo;
    }

    private static GpsInHbaseVo getGpsMsg(List<String> gpars,GpsInHbaseVo gpsNewHbaseVo) {
        //时间输出
        Integer dateY = getChar2Integer(gpars.get(2));
        System.out.println(gpars.get(2)+"---"+dateY);
        Integer dateM = getChar2Integer(gpars.get(3));
        System.out.println(gpars.get(3)+"---"+dateM);
        Integer dateD = getChar2Integer(gpars.get(4));
        System.out.println(gpars.get(4)+"---"+dateD);
        Integer dateH = getChar2Integer(gpars.get(5));
        System.out.println(gpars.get(5)+"---"+dateH);
        Integer dateMN = getChar2Integer(gpars.get(6));
        System.out.println(gpars.get(6)+"---"+dateMN);
        Integer dateS = getChar2Integer(gpars.get(7));
        System.out.println(gpars.get(7)+"---"+dateS);
        Integer msl = getChar2Integer(gpars.get(8));
        Integer msh = getChar2Integer(gpars.get(9));
        BigDecimal bX = new BigDecimal(msh*256+msl);
        Calendar ca = Calendar.getInstance();
        ca.set(Calendar.YEAR,Integer.parseInt("20"+dateY));
        ca.set(Calendar.MONTH,dateM-1);
        ca.set(Calendar.DAY_OF_MONTH,dateD);
        ca.set(Calendar.HOUR_OF_DAY,dateH);
        ca.set(Calendar.MINUTE,dateMN);
        ca.set(Calendar.SECOND,dateS);
        ca.set(Calendar.MILLISECOND,bX.intValue());
        gpsNewHbaseVo.setInitialBjTime(DateUtils.fetchWholeSecond(ca.getTime().getTime()).toString());

        //加速度输出
        Integer axl = getChar2Integer(gpars.get(13));
        Integer axh = getChar2Integer(gpars.get(14));
        BigDecimal ax = new BigDecimal((axh*256+axl));
        ax = ax.divide(new BigDecimal(32768),8,BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(16));
        gpsNewHbaseVo.setAxSpead(ax.doubleValue()+"");
        Integer ayl = getChar2Integer(gpars.get(15));
        Integer ayh = getChar2Integer(gpars.get(16));
        BigDecimal ay = new BigDecimal((ayh*256+ayl));
        ay = ay.divide(new BigDecimal(32768),8,BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(16));
        gpsNewHbaseVo.setAySpead(ay.doubleValue()+"");
        Integer azl = getChar2Integer(gpars.get(17));
        Integer azh = getChar2Integer(gpars.get(18));
        BigDecimal az = new BigDecimal((azh*256+azl));
        az = az.divide(new BigDecimal(32768),8,BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(16));
        gpsNewHbaseVo.setAzSpead(az.doubleValue()+"");
        Integer atl = getChar2Integer(gpars.get(19));
        Integer ath = getChar2Integer(gpars.get(20));
        BigDecimal at = new BigDecimal((ath*256+atl));
        at = at.divide(new BigDecimal(100),2,BigDecimal.ROUND_HALF_DOWN);
        gpsNewHbaseVo.setTlSpead(at.doubleValue()+"");

        //角速度输出
        Integer wxl = getChar2Integer(gpars.get(24));
        Integer wxh = getChar2Integer(gpars.get(25));
        BigDecimal wx = new BigDecimal((wxh*256+wxl));
        wx = wx.divide(new BigDecimal(32768),8,BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(2000));
        gpsNewHbaseVo.setWxSpead(wx.doubleValue()+"");
        Integer wyl = getChar2Integer(gpars.get(26));
        Integer wyh = getChar2Integer(gpars.get(27));
        BigDecimal wy = new BigDecimal((wyh*256+wyl));
        wy = wy.divide(new BigDecimal(32768),8,BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(2000));
        gpsNewHbaseVo.setWySpead(wy.doubleValue()+"");
        Integer wzl = getChar2Integer(gpars.get(28));
        Integer wzh = getChar2Integer(gpars.get(29));
        BigDecimal wz = new BigDecimal((wzh*256+wzl));
        wz = wz.divide(new BigDecimal(32768),8,BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(2000));
        gpsNewHbaseVo.setWzSpead(wz.doubleValue()+"");
        Integer vtl = getChar2Integer(gpars.get(30));
        Integer vth = getChar2Integer(gpars.get(31));
        BigDecimal vt = new BigDecimal((vth*256+vtl));
        vt = vt.divide(new BigDecimal(100),2,BigDecimal.ROUND_HALF_DOWN);
        gpsNewHbaseVo.setVoSpead(vt.doubleValue()+"");

        //角度输出
        Integer roiil = getChar2Integer(gpars.get(35));
        Integer roiih = getChar2Integer(gpars.get(36));
        BigDecimal ro = new BigDecimal((roiih*256+roiil));
        ro = ro.divide(new BigDecimal(32768),8,BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(180));
        gpsNewHbaseVo.setRollSpead(ro.doubleValue()+"");
        Integer pitchl = getChar2Integer(gpars.get(37));
        Integer pitchh = getChar2Integer(gpars.get(38));
        BigDecimal pi = new BigDecimal((pitchh*256+pitchl));
        pi = pi.divide(new BigDecimal(32768),8,BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(180));
        gpsNewHbaseVo.setPitchRoll(pi.doubleValue()+"");
        Integer yawl = getChar2Integer(gpars.get(39));
        Integer yawh = getChar2Integer(gpars.get(40));
        BigDecimal ya = new BigDecimal((yawh*256+yawl));
        ya = ya.divide(new BigDecimal(32768),8,BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(180));
        gpsNewHbaseVo.setYawRoll(ya.doubleValue()+"");
        Integer vl = getChar2Integer(gpars.get(41));
        Integer vh = getChar2Integer(gpars.get(42));
        BigDecimal vst = new BigDecimal((vh*256+vl));
        gpsNewHbaseVo.setVersionRoll(vst.intValue()+"");

        //经纬度输出
        Integer lon0 = getChar2Integer(gpars.get(46));
        Integer lon1 = getChar2Integer(gpars.get(47));
        Integer lon2 = getChar2Integer(gpars.get(48));
        Integer lon3 = getChar2Integer(gpars.get(49));
        BigDecimal lon = new BigDecimal((lon3*256*256*256+lon2*256*256+lon1*256+lon0));
        double lond = lon.doubleValue()%10000000;
        lon = lon.divide(new BigDecimal(10000000),0,BigDecimal.ROUND_HALF_DOWN);
        gpsNewHbaseVo.setLongitude(lon.doubleValue()+(new BigDecimal(lond).divide(new BigDecimal(10000000),8,BigDecimal.ROUND_HALF_DOWN).doubleValue())+"");
        Integer lat0 = getChar2Integer(gpars.get(50));
        Integer lat1 = getChar2Integer(gpars.get(51));
        Integer lat2 = getChar2Integer(gpars.get(52));
        Integer lat3 = getChar2Integer(gpars.get(53));
        BigDecimal lat = new BigDecimal((lat3*256*256*256+lat2*256*256+lat1*256+lat0));
        double latd = lat.doubleValue()%10000000;
        lat = lat.divide(new BigDecimal(10000000),0,BigDecimal.ROUND_HALF_DOWN);
        gpsNewHbaseVo.setLatitude(lat.doubleValue()+(new BigDecimal(latd).divide(new BigDecimal(10000000),8,BigDecimal.ROUND_HALF_DOWN).doubleValue())+"");

        //GPS数据输出
        Integer gpsl = getChar2Integer(gpars.get(57));
        Integer gpsh = getChar2Integer(gpars.get(58));
        BigDecimal gpst = new BigDecimal((gpsh*256+gpsl));
        gpst = gpst.divide(new BigDecimal(10),1,BigDecimal.ROUND_HALF_DOWN);
        gpsNewHbaseVo.setGpsHeight(gpst.doubleValue()+"");
        Integer gpsyl = getChar2Integer(gpars.get(59));
        Integer gpsyh = getChar2Integer(gpars.get(60));
        BigDecimal gpsyt = new BigDecimal((gpsyh*256+gpsyl));
        gpsyt = gpsyt.divide(new BigDecimal(100),1,BigDecimal.ROUND_HALF_DOWN);
        gpsNewHbaseVo.setGpsYaw(gpsyt.doubleValue()+"");
        Integer gpsv0 = getChar2Integer(gpars.get(61));
        Integer gpsv1 = getChar2Integer(gpars.get(62));
        Integer gpsv2 = getChar2Integer(gpars.get(63));
        Integer gpsv3 = getChar2Integer(gpars.get(64));
        BigDecimal gpsv = new BigDecimal((gpsv3*256*256*256+gpsv2*256*256+gpsv1*256+gpsv0));
        gpsv = gpsv.divide(new BigDecimal(1000),3,BigDecimal.ROUND_HALF_DOWN);
        gpsNewHbaseVo.setGroundRate(gpsv.doubleValue()+"");

        //GPS定位精度输出
        Integer snl = getChar2Integer(gpars.get(68));
        Integer snh = getChar2Integer(gpars.get(69));
        BigDecimal snt = new BigDecimal((snh*256+snl));
        gpsNewHbaseVo.setSnl(snt.intValue()+"");
        Integer pdl = getChar2Integer(gpars.get(70));
        Integer pdh = getChar2Integer(gpars.get(71));
        BigDecimal pdop = new BigDecimal((pdh*256+pdl));
        pdop = pdop.divide(new BigDecimal(100),2,BigDecimal.ROUND_HALF_DOWN);
        gpsNewHbaseVo.setDefinition(pdop.doubleValue()+"");
        Integer hdl = getChar2Integer(gpars.get(72));
        Integer hdh = getChar2Integer(gpars.get(73));
        BigDecimal hdop = new BigDecimal((hdh*256+hdl));
        hdop = hdop.divide(new BigDecimal(100),2,BigDecimal.ROUND_HALF_DOWN);
        gpsNewHbaseVo.setGnsDefinition(hdop.doubleValue()+"");
        Integer vdol = getChar2Integer(gpars.get(74));
        Integer vdoh = getChar2Integer(gpars.get(75));
        BigDecimal vdop = new BigDecimal((vdoh*256+vdol));
        vdop = vdop.divide(new BigDecimal(100),2,BigDecimal.ROUND_HALF_DOWN);
        gpsNewHbaseVo.setVerDefinition(vdop.doubleValue()+"");

        return gpsNewHbaseVo;

    }

    public static Integer getChar2Integer(String inter){
        Integer h = Integer.valueOf(inter, 16);
        return h;
    }

}
