package com.snct.analysis.vo;

import com.snct.common.annotation.Excel;
import com.snct.common.annotation.Excel;

/**
 * @description: Racdu1数据
 * Example:$RIRSA,01.1,A,,*04
 * $RIRSA,<1>,A,,*CS<CR><LF>
 * 逗号隔开
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class RacduHbaseVo {

    private String id;

    /**
     * 录入时间
     */
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    private String initialBjTime;
    /**
     * 1.自动舵转向率
     */
    @Excel(name="自动舵转向率")
    private String autopilotRate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAutopilotRate() {
        return autopilotRate;
    }

    public void setAutopilotRate(String autopilotRate) {
        this.autopilotRate = autopilotRate;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }
}
