package com.snct.analysis.vo;


import com.snct.common.annotation.Excel;
import com.snct.common.annotation.Excel;

/**
 * @description:  EA600数据 1秒一组
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Ea600HbaseVo {

    private String id;

    /**
     * 录入时间
     */
    private String initialTime;
    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    private String initialBjTime;

    /**
     * water depth (in feet) 水深
     */
    @Excel(name="水深")
    private String waterDepthF;
    /**
     * 2.水深单位 英尺
     */
    @Excel(name="单位 英尺")
    private String waterUnitF;
    /**
     * water depth (in meters) 水深
     */
    @Excel(name="水深")
    private String waterDepthM;
    /**
     * 4.水深单位 米
     */
    @Excel(name="单位 米")
    private String waterUnitM;
    /**
     * 5.water depth (in fathoms)
     */
    private String waterDepthI;

    /**
     * 纬度
     */
    private String latitude;
    /**
     * 经度
     */
    private String longitude;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getWaterDepthF() {
        return waterDepthF;
    }

    public void setWaterDepthF(String waterDepthF) {
        this.waterDepthF = waterDepthF;
    }

    public String getWaterUnitF() {
        return waterUnitF;
    }

    public void setWaterUnitF(String waterUnitF) {
        this.waterUnitF = waterUnitF;
    }

    public String getWaterDepthM() {
        return waterDepthM;
    }

    public void setWaterDepthM(String waterDepthM) {
        this.waterDepthM = waterDepthM;
    }

    public String getWaterUnitM() {
        return waterUnitM;
    }

    public void setWaterUnitM(String waterUnitM) {
        this.waterUnitM = waterUnitM;
    }

    public String getWaterDepthI() {
        return waterDepthI;
    }

    public void setWaterDepthI(String waterDepthI) {
        this.waterDepthI = waterDepthI;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }
}
