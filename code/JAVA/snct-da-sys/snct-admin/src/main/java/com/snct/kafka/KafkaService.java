package com.snct.kafka;

import com.alibaba.fastjson2.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadLocalRandom;

@Service
public class KafkaService {
    private static final Logger log = LoggerFactory.getLogger(KafkaService.class);

    private static String NET_DEVICE_SOURCE_DATA_TOPIC = "snct_device_source_data_topic";
    public static KafkaTemplate<String, String> kafkaTemplate;


    // 并发发送消息（使用 CompletableFuture）
    @Async
    public CompletableFuture<Void> send2Kafka(KafkaMessage message) {
        return CompletableFuture.runAsync(() -> {
            try {
                // 模拟处理延迟
                Thread.sleep(ThreadLocalRandom.current().nextInt(50, 200));
                if(kafkaTemplate!=null){
                    kafkaTemplate.send(NET_DEVICE_SOURCE_DATA_TOPIC, JSONObject.toJSONString(message));
                    log.debug("并发消息已发送: {}", message);
                }else{
                    log.error("发送数据失败：网络发送开关未打开");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }











//    // 同步发送消息
//    public void sendSyncMessage(String topic, String key, String message) {
//        try {
//            SendResult<String, String> result = kafkaTemplate.send(topic, key, message).get();
//            log.info("同步发送成功! Partition: {}, Offset: {}",
//                    result.getRecordMetadata().partition(),
//                    result.getRecordMetadata().offset());
//        } catch (InterruptedException | ExecutionException e) {
//            log.error("同步消息发送失败", e);
//            Thread.currentThread().interrupt();
//        }
//    }
//
//    // 异步发送消息
//    public void sendAsyncMessage(String topic, String key, String message) {
//        kafkaTemplate.send(topic, key, message)
//                .addCallback(
//                        result -> {
//                            if (result != null) {
//                                log.info("异步发送成功! Partition: {}, Offset: {}",
//                                        result.getRecordMetadata().partition(),
//                                        result.getRecordMetadata().offset());
//                            }
//                        },
//                        ex -> log.error("异步消息发送失败: {}", ex.getMessage(), ex)
//                );
//    }
//    // 定时发送演示（每3秒发送10条并发消息）
//    @Scheduled(fixedRate = 3000)
//    public void scheduledConcurrentSend() {
//        String topic = "spring-kafka-topic-test";
//        //for (int i = 0; i < 10; i++) {
//            //String key = "key-" + System.currentTimeMillis(); // 3个不同的key
//            String message = "并发消息 #" + UUID.randomUUID().toString().substring(0, 6);
//            send2Kafka(topic, message);
//        //}
//
//        log.info("已调度1条并发消息发送");
//    }
}