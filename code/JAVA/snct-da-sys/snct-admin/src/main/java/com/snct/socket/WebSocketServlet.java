package com.snct.socket;

import com.snct.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;

/**
 * @description:
 * @author: snct
 **/
@ServerEndpoint(value = "/websocket/{code}")
@Component
public class WebSocketServlet {

    private Logger logger = LoggerFactory.getLogger(WebSocketServlet.class);

    private ScheduledExecutorService scheduled = null;
    private ScheduledFuture<?> future = null;
    /**
     * 存放session以及对应的页面
     */
    private static ConcurrentHashMap<Session, Map<String, String>> hashMap = new ConcurrentHashMap<Session, Map<String, String>>();
    /**
     * 判断是否启动线程
     */
    private static boolean isRun = true;
    private Session session;

    /**
     * @ClassName: onOpen
     * @Description: 开启连接的操作
     */
    @OnOpen
    public void onOpen(@PathParam(value = "code") String code, Session session) {
        this.session = session;
        logger.info("WebSocket接收到设备请求---编号:--{}",code);
        if ( StringUtils.isBlank(code) ) {
            return;
        }
        Map<String, String> map = hashMap.get(session);
        map.put(code, code);
        hashMap.put(session,map);
    }

    /**
     * @ClassName: onClose
     * @Description: 连接关闭的操作
     */
    @OnClose
    public void onClose() {
        logger.info("WebSocket断开。。session:{}", session.getId());
        hashMap.remove(this.session);
    }

    /**
     * 给服务器发送消息
     *
     * @param msg 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String msg, Session session) {
        Map<String, String> map = hashMap.get(session);
        System.out.println("收到设备编号"+map.get("code")+"消息："+msg);
    }

    /**
     * @ClassName: OnError
     * @Description: 出错的操作
     */
    @OnError
    public void onError(Session session, Throwable error) {
        error.printStackTrace();
    }

    /**
     * 获取最新的session集合
     *
     * @return
     */
    public ConcurrentHashMap<Session, Map<String, String>> getSessionMap() {
        return hashMap;
    }

}