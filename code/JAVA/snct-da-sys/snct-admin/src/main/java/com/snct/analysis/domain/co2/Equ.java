package com.snct.analysis.domain.co2;

import com.snct.system.domain.msg.Instrument;

/**
 * @description: co2的EQU数据
 * Example: EQU	0	13/07/19	00:05:40	29.91	NaN	420.39
 * <1> <2> <3> <4> <5> <6> <7> <8> <9> <10> <11> <12> <13> <14> <15> <16> <17> <18> <19><20> <21> <22> <23> <24> <25> <26> <27> <28> <29> <30><31> <32> <33> <34> <35> <36> <37> <38> <39> <40><41> <42> <43> <44>
 * 数据以空格隔开
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Equ extends Instrument {

    /**
     * 1.  测量类型 kind, EQU=实际测量值，可能还有如ATM   STD1  STD4 ，先记录下来
     */
    private String measureType;
    /**
     * 2.错误代码 err 0=没有错误
     */
    private String errorCode;
    /**
     * 3. PC日期 PC_date  DD/MM/YY
     */
    private String pcDate;
    /**
     * 4. PC 时间 PC_time   HH:MM:SS
     */
    private String pcTime;
    /**
     * 5. 平衡器温度   equ_temp
     */
    private String equTemp;
    /**
     * 6. 标准气标称值 std_val,没有数值为 NAN
     */
    private String stdVal;
    /**
     * 7.   CO2 检测值co2 (ppm),
     */
    private String co2;
    /**
     * 8.校正后的CO2值co2_dry（ppm），  *用于参数显示
     */
    private String co2Dry;
    /**
     * 9.  CH4检测值 CH4(ppm)
     */
    private String ch4;
    /**
     * 10. 校正后的CH4值CH4_dry (ppm)，CH4_dry
     */
    private String ch4Dry;
    /**
     * 11. 水汽含量 H2O (%) ，
     */
    private String h2o;
    /**
     * 12.  平衡器大气压力 equ_press（atm）
     */
    private String equPress;
    /**
     * 13.进入系统水流量H20_flow（L/min）
     */
    private String h20Flow;
    /**
     * 14. 气路中气流量licor_flow (ml/min)
     */
    private String licorFlow;
    /**
     * 15. 平衡器气泵值 equ_pump
     */
    private String equPump;
    /**
     * 16. 预平衡器进气/出气流量vent_flow (ml/min)， 正值为进气，负值为出气
     * 进出气
     */
    private String inOutFlow;
    /**
     * 17.大气气路水汽值 atm_cond
     */
    private String atmCond;
    /**
     * 18.平衡器气路水汽值 equ_cond
     */
    private String equCond;
    /**
     * 19.  drip1
     */
    private String drip1;
    /**
     * 20. drip2
     */
    private String drip2;
    /**
     * 21.  cond_temp
     */
    private String condTemp;
    /**
     * 22.  干箱温度 dry_box_temp (℃)
     */
    private String dryBoxTemp;
    /**
     * 23.溶氧值 Oxygen（μmol/L）
     */
    private String oxygen;
    /**
     * 24. 溶氧饱和度Saturation（%）
     */
    private String saturation;
    /**
     * 25. 溶氧传感器温度 Temperature（℃）
     */
    private String temperature;
    /**
     * 26. SBE45传感器温度 SBE_Temp（℃）
     */
    private String sbeTemp;
    /**
     * 27. SBE45电导率Conductivity
     */
    private String conductivity;
    /**
     * 28. SBE45盐度 Salinity
     */
    private String salinity;
    /**
     * 29. GPS日期 GPS_Date
     */
    private String gpsDate;
    /**
     * 30.GPS时间 GPS_Time
     */
    private String gpsTime;
    /**
     * 31. 纬度  Latitude
     */
    private String latitude;
    /**
     * 32.   经度  Longitude
     */
    private String longitude;
    /**
     * 33.SBE21记录个数 TSG_count
     */
    private String tsgCount;
    /**
     * 34.  SBE21实验室温度传感器温度 TSG_Temp1（℃）
     */
    private String tsgTemp1;
    /**
     * 35. SBE21原位传感器温度 TSG_temp2（℃）
     */
    private String tsgTemp2;
    /**
     * 36. SBE21实验室盐度传感器值
     */
    private String sbeSalinity;
    /**
     * 37. SBE21 实验室溶氧值 DO （mg/L）
     */
    private String dissolvedOxygen;
    /**
     * 38.SBE21 实验室叶绿素值 Chl (mg/m^3)
     */
    private String chl;
    /**
     * 39.  SBE21 实验室浊度值 Turb（NTU）
     */
    private String turb;
    /**
     * 40. 气温 Air_temp_degC（摄氏度）
     */
    private String airTemp;
    /**
     * 41. 湿度 Humidity_percent（%）
     */
    private String humidity;
    /**
     * 42.  气压Atm_press_hPa(hPa)
     */
    private String atmPress;
    /**
     * 43.真风向True_wind_dir
     */
    private String trueWindDir;
    /**
     * 44. 真风速 True_wind_sp(m/s)
     */
    private String trueWindSp;


    @Override
    public void dataAnalysis(String dataStr) {
        String[] values = dataStr.split("\\t", -1);
        values = super.valuesTrim(values);
        measureType = values[0];
        errorCode = values[1];
        pcDate = values[2];
        pcTime = values[3];
        equTemp = values[4];
        stdVal = values[5];
        co2 = values[6];
        co2Dry = values[7];
        ch4 = values[8];
        ch4Dry = values[9];
        h2o = values[10];
        equPress = values[11];
        h20Flow = values[12];
        licorFlow = values[13];
        equPump = values[14];
        inOutFlow = values[15];
        atmCond = values[16];
        equCond = values[17];
        drip1 = values[18];
        drip2 = values[19];
        condTemp = values[20];
        dryBoxTemp = values[21];
        oxygen = values[22];
        saturation = values[23];
        temperature = values[24];
        sbeTemp = values[25];
        conductivity = values[26];
        salinity = values[27];
        gpsDate = values[28];
        gpsTime = values[29];
        latitude = values[30];
        longitude = values[31];
        tsgCount = values[32];
        tsgTemp1 = values[33];
        tsgTemp2 = values[34];
        sbeSalinity = values[35];
        dissolvedOxygen = values[36];
        chl = values[37];
        //12.23出船 co2少一组数据 SBE21 实验室浊度值 Turb（NTU）  无值
//        turb= values[38]);
        if (values.length > 38) {
            airTemp = values[38];
        }
        if (values.length > 39) {
            humidity = values[39];
        }
        if (values.length > 40) {
            atmPress = values[40];
        }
        if (values.length > 41) {
            trueWindDir = values[41];
        }
        if (values.length > 42) {
            trueWindSp = values[42];
        }
    }

    public String getMeasureType() {
        return measureType;
    }

    public void setMeasureType(String measureType) {
        this.measureType = measureType;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getPcDate() {
        return pcDate;
    }

    public void setPcDate(String pcDate) {
        this.pcDate = pcDate;
    }

    public String getPcTime() {
        return pcTime;
    }

    public void setPcTime(String pcTime) {
        this.pcTime = pcTime;
    }

    public String getEquTemp() {
        return equTemp;
    }

    public void setEquTemp(String equTemp) {
        this.equTemp = equTemp;
    }

    public String getStdVal() {
        return stdVal;
    }

    public void setStdVal(String stdVal) {
        this.stdVal = stdVal;
    }

    public String getCo2() {
        return co2;
    }

    public void setCo2(String co2) {
        this.co2 = co2;
    }

    public String getCo2Dry() {
        return co2Dry;
    }

    public void setCo2Dry(String co2Dry) {
        this.co2Dry = co2Dry;
    }

    public String getCh4() {
        return ch4;
    }

    public void setCh4(String ch4) {
        this.ch4 = ch4;
    }

    public String getCh4Dry() {
        return ch4Dry;
    }

    public void setCh4Dry(String ch4Dry) {
        this.ch4Dry = ch4Dry;
    }

    public String getH2o() {
        return h2o;
    }

    public void setH2o(String h2o) {
        this.h2o = h2o;
    }

    public String getEquPress() {
        return equPress;
    }

    public void setEquPress(String equPress) {
        this.equPress = equPress;
    }

    public String getH20Flow() {
        return h20Flow;
    }

    public void setH20Flow(String h20Flow) {
        this.h20Flow = h20Flow;
    }

    public String getLicorFlow() {
        return licorFlow;
    }

    public void setLicorFlow(String licorFlow) {
        this.licorFlow = licorFlow;
    }

    public String getEquPump() {
        return equPump;
    }

    public void setEquPump(String equPump) {
        this.equPump = equPump;
    }

    public String getInOutFlow() {
        return inOutFlow;
    }

    public void setInOutFlow(String inOutFlow) {
        this.inOutFlow = inOutFlow;
    }

    public String getAtmCond() {
        return atmCond;
    }

    public void setAtmCond(String atmCond) {
        this.atmCond = atmCond;
    }

    public String getEquCond() {
        return equCond;
    }

    public void setEquCond(String equCond) {
        this.equCond = equCond;
    }

    public String getDrip1() {
        return drip1;
    }

    public void setDrip1(String drip1) {
        this.drip1 = drip1;
    }

    public String getDrip2() {
        return drip2;
    }

    public void setDrip2(String drip2) {
        this.drip2 = drip2;
    }

    public String getCondTemp() {
        return condTemp;
    }

    public void setCondTemp(String condTemp) {
        this.condTemp = condTemp;
    }

    public String getDryBoxTemp() {
        return dryBoxTemp;
    }

    public void setDryBoxTemp(String dryBoxTemp) {
        this.dryBoxTemp = dryBoxTemp;
    }

    public String getOxygen() {
        return oxygen;
    }

    public void setOxygen(String oxygen) {
        this.oxygen = oxygen;
    }

    public String getSaturation() {
        return saturation;
    }

    public void setSaturation(String saturation) {
        this.saturation = saturation;
    }

    public String getTemperature() {
        return temperature;
    }

    public void setTemperature(String temperature) {
        this.temperature = temperature;
    }

    public String getSbeTemp() {
        return sbeTemp;
    }

    public void setSbeTemp(String sbeTemp) {
        this.sbeTemp = sbeTemp;
    }

    public String getConductivity() {
        return conductivity;
    }

    public void setConductivity(String conductivity) {
        this.conductivity = conductivity;
    }

    public String getSalinity() {
        return salinity;
    }

    public void setSalinity(String salinity) {
        this.salinity = salinity;
    }

    public String getGpsDate() {
        return gpsDate;
    }

    public void setGpsDate(String gpsDate) {
        this.gpsDate = gpsDate;
    }

    public String getGpsTime() {
        return gpsTime;
    }

    public void setGpsTime(String gpsTime) {
        this.gpsTime = gpsTime;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getTsgCount() {
        return tsgCount;
    }

    public void setTsgCount(String tsgCount) {
        this.tsgCount = tsgCount;
    }

    public String getTsgTemp1() {
        return tsgTemp1;
    }

    public void setTsgTemp1(String tsgTemp1) {
        this.tsgTemp1 = tsgTemp1;
    }

    public String getTsgTemp2() {
        return tsgTemp2;
    }

    public void setTsgTemp2(String tsgTemp2) {
        this.tsgTemp2 = tsgTemp2;
    }

    public String getSbeSalinity() {
        return sbeSalinity;
    }

    public void setSbeSalinity(String sbeSalinity) {
        this.sbeSalinity = sbeSalinity;
    }

    public String getDissolvedOxygen() {
        return dissolvedOxygen;
    }

    public void setDissolvedOxygen(String dissolvedOxygen) {
        this.dissolvedOxygen = dissolvedOxygen;
    }

    public String getChl() {
        return chl;
    }

    public void setChl(String chl) {
        this.chl = chl;
    }

    public String getTurb() {
        return turb;
    }

    public void setTurb(String turb) {
        this.turb = turb;
    }

    public String getAirTemp() {
        return airTemp;
    }

    public void setAirTemp(String airTemp) {
        this.airTemp = airTemp;
    }

    public String getHumidity() {
        return humidity;
    }

    public void setHumidity(String humidity) {
        this.humidity = humidity;
    }

    public String getAtmPress() {
        return atmPress;
    }

    public void setAtmPress(String atmPress) {
        this.atmPress = atmPress;
    }

    public String getTrueWindDir() {
        return trueWindDir;
    }

    public void setTrueWindDir(String trueWindDir) {
        this.trueWindDir = trueWindDir;
    }

    public String getTrueWindSp() {
        return trueWindSp;
    }

    public void setTrueWindSp(String trueWindSp) {
        this.trueWindSp = trueWindSp;
    }

}
