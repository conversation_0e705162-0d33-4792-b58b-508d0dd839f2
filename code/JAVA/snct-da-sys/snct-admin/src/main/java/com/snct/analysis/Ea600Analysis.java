package com.snct.analysis;

import com.snct.kafka.KafkaMessage;
import com.snct.analysis.vo.Ea600HbaseVo;
import com.snct.common.utils.DateUtils;
import com.snct.analysis.domain.ea600.Sddbs;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: ea600
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Ea600Analysis {
    protected static Logger logger = LoggerFactory.getLogger(Ea600Analysis.class);

    private static final String TEMP_KEY = "EA600_TEMP";

    /**
     * 从一组ea600数据中，取得有用的字段
     *
     * @param kafkaMessage
     * @return
     */
    public static Ea600HbaseVo getEa600List(KafkaMessage kafkaMessage) {

        Ea600HbaseVo ea600HbaseVo = null;

        List<String> temp = BaseAnalysis.getTempList(TEMP_KEY, kafkaMessage.getCode());
        if (temp == null) {
            temp = new ArrayList<>();
        }

        try {
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime();

            // 判断时间是否为同一秒
            boolean isSame = BaseAnalysis.isSameTime(TEMP_KEY, kafkaMessage.getCode(), currentTime);

            // 同一秒的数据不做发送，在下一秒后再一起合并
            // 去掉多余的字符
            if (kafkaMessage.getMsg().contains("\u0000")) {
                kafkaMessage.setMsg(kafkaMessage.getMsg().replace("\u0000", ""));
            }
            if (!CollectionUtils.isEmpty(temp) && kafkaMessage.getMsg().startsWith("$SDDPT") && !isSame) {
                ea600HbaseVo = new Ea600HbaseVo();
                ea600HbaseVo.setInitialTime(DateUtils.fetchWholeSecond(currentTime).toString());
                for (String line : temp) {
                    translateLine(ea600HbaseVo, line);
                }
                temp.clear();
            }

            temp.add(kafkaMessage.getMsg());

            BaseAnalysis.updateTemp(TEMP_KEY, kafkaMessage.getCode(), temp, currentTime);
        } catch (Exception e) {
            logger.error("EA600解析出错,---{}", e);
            temp.clear();
        }

        return ea600HbaseVo;
    }

    /**
     * 解析预览
     *
     * @param kafkaMessage
     * @return
     */
    public static Ea600HbaseVo getParseEa600List(List<KafkaMessage> kafkaMessage) {

        Ea600HbaseVo ea600HbaseVo = null;

        List<String> temp = new ArrayList<>();
        for (KafkaMessage kafkaMessage1:
                kafkaMessage) {
            if (kafkaMessage1.getMsg().contains("\u0000")) {
                kafkaMessage1.setMsg(kafkaMessage1.getMsg().replace("\u0000", ""));
            }
            if (kafkaMessage1.getMsg().startsWith("$SDDPT")){
                temp.add(kafkaMessage1.getMsg());
            }

        }

        try {
//            long currentTime = kafkaMessage.get(0).getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.get(0).getInitialTime();

                ea600HbaseVo = new Ea600HbaseVo();
//                ea600HbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(currentTime,"yyyy-MM-dd HH:mm:ss").toString());
                for (String line : temp) {
                    translateLine(ea600HbaseVo, line);
                }
            if (AwsAnalysis.checkObjAllFieldsIsNull(ea600HbaseVo)){
                ea600HbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss").toString());
                ea600HbaseVo.setInitialTime(null);
            }else{
                return null;
            }
                temp.clear();

        } catch (Exception e) {
            logger.error("EA600解析出错,---{}", e);
            temp.clear();
        }

        return ea600HbaseVo;
    }

    /**
     * 转换一行数据
     *
     * @param ea600HbaseVo line
     * @return
     */
    private static void translateLine(Ea600HbaseVo ea600HbaseVo, String line) {
        if (line.startsWith("@&")) {
            return;
        }
        String prefix = line.substring(0, 6);
        switch (prefix) {
            case "$SDDPT": {

            }
            break;
            case "$SDDBS": {
                Sddbs sddbs = new Sddbs();
                sddbs.dataAnalysis(line);

                BeanUtils.copyProperties(sddbs, ea600HbaseVo);
            }
            break;
            default: {
            }
        }
    }
}
