package com.snct.task;

import com.snct.device.manager.DeviceConnectionManager;
import com.snct.system.domain.Device;
import com.snct.system.service.IDeviceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 简化的设备连接状态检测定时任务
 * 基于Redis Key过期机制检测设备连接状态
 * 
 * <AUTHOR>
 */
@Component
public class DeviceStatusTask {
    
    private static final Logger logger = LoggerFactory.getLogger(DeviceStatusTask.class);
    
    @Autowired
    private DeviceConnectionManager connectionManager;
    
    @Autowired
    private IDeviceService deviceService;
    
    /**
     * 检测所有设备连接状态
     * 每2分钟执行一次，基于Redis Key是否存在判断设备连接状态
     */
    //@Scheduled(fixedRate = 120000)
    public void checkAllDeviceStatus() {
        logger.debug("开始执行设备连接状态检测任务");
        
        try {
            // 检测SNMP设备（Modem等）
            checkSnmpDevices();
            
            // 检测TCP设备（PDU等）
            checkTcpDevices();
            
            // 检测串口设备
            checkSerialDevices();
            
            // 检测HTTP设备
            checkHttpDevices();
            
        } catch (Exception e) {
            logger.error("设备连接状态检测任务执行异常", e);
        }
        
        logger.debug("设备连接状态检测任务执行完成");
    }
    
    /**
     * 检测SNMP设备连接状态
     */
    private void checkSnmpDevices() {
        try {
            Device query = new Device();
            query.setType(52); // Modem设备类型
            //query.setEnable(1);
            List<Device> snmpDevices = deviceService.selectDeviceList(query);
            
            connectionManager.checkAndUpdateDeviceStatus(snmpDevices, 
                DeviceConnectionManager.ProtocolType.SNMP);
            
            logger.debug("SNMP设备连接状态检测完成，检测设备数: {}", snmpDevices.size());
        } catch (Exception e) {
            logger.error("SNMP设备连接状态检测异常", e);
        }
    }
    
    /**
     * 检测TCP设备连接状态
     */
    private void checkTcpDevices() {
        try {
            Device query = new Device();
            query.setType(51); // PDU设备类型
            //query.setEnable(1);
            List<Device> tcpDevices = deviceService.selectDeviceList(query);
            
            connectionManager.checkAndUpdateDeviceStatus(tcpDevices, 
                DeviceConnectionManager.ProtocolType.TCP);
            
            logger.debug("TCP设备连接状态检测完成，检测设备数: {}", tcpDevices.size());
        } catch (Exception e) {
            logger.error("TCP设备连接状态检测异常", e);
        }
    }
    
    /**
     * 检测串口设备连接状态
     */
    private void checkSerialDevices() {
        try {
            Device query = new Device();
            //query.setEnable(1);
            List<Device> allDevices = deviceService.selectDeviceList(query);
            
            // 筛选出有串口配置的设备
            List<Device> serialDevices = allDevices.stream()
                .filter(device -> device.getSerialPort() != null && !device.getSerialPort().isEmpty())
                .collect(Collectors.toList());
            
            connectionManager.checkAndUpdateDeviceStatus(serialDevices, 
                DeviceConnectionManager.ProtocolType.SERIAL);
            
            logger.debug("串口设备连接状态检测完成，检测设备数: {}", serialDevices.size());
        } catch (Exception e) {
            logger.error("串口设备连接状态检测异常", e);
        }
    }
    
    /**
     * 检测HTTP设备连接状态
     */
    private void checkHttpDevices() {
        try {
            Device query = new Device();
            //query.setEnable(1);
            List<Device> allDevices = deviceService.selectDeviceList(query);
            
            // 筛选出HTTP设备
            List<Device> httpDevices = allDevices.stream()
                .filter(device -> device.getCode() != null && device.getCode().startsWith("033"))
                .collect(Collectors.toList());
            
            connectionManager.checkAndUpdateDeviceStatus(httpDevices, 
                DeviceConnectionManager.ProtocolType.HTTP);
            
            logger.debug("HTTP设备连接状态检测完成，检测设备数: {}", httpDevices.size());
        } catch (Exception e) {
            logger.error("HTTP设备连接状态检测异常", e);
        }
    }
}
