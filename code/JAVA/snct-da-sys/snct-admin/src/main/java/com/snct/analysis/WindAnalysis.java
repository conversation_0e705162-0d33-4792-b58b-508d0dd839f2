package com.snct.analysis;

import com.snct.kafka.KafkaMessage;
import com.snct.analysis.vo.WindHbaseVo;
import com.snct.common.utils.DateUtils;
import com.snct.analysis.domain.wind.Iimwv;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 探测仪
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class WindAnalysis {
    protected static Logger logger = LoggerFactory.getLogger(WindAnalysis.class);

    private static final String TEMP_KEY = "WIND_TEMP";

    // =====================新的=====================
    public static WindHbaseVo getWindList2(KafkaMessage kafkaMessage) {
        WindHbaseVo windHbaseVo = null;
        logger.info("进来开始解析");
        List<String> temp = BaseAnalysis.getTempList(TEMP_KEY, kafkaMessage.getCode());
        if (temp == null) {
            temp = Lists.newArrayList();
        }
        try {
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime().longValue();
            logger.info("进来开始解析2");
            boolean isSame = BaseAnalysis.isSameTime(TEMP_KEY, kafkaMessage.getCode(), Long.valueOf(currentTime));
            logger.info("进来开始解析3");
            if (!CollectionUtils.isEmpty(temp) && kafkaMessage.getMsg().startsWith("$WIMWV") && !isSame) {
                logger.info("进来开始解析4--{}", Integer.valueOf(temp.size()));
                windHbaseVo = new WindHbaseVo();
                windHbaseVo.setInitialTime(DateUtils.fetchWholeSecond(Long.valueOf(currentTime)).toString());
                for (String line : temp) {
                    translateLine(windHbaseVo, line);
                }
                temp.clear();
            }
            temp.add(kafkaMessage.getMsg());
            logger.info("进来开始解析结束");
            BaseAnalysis.updateTemp(TEMP_KEY, kafkaMessage.getCode(), temp, Long.valueOf(currentTime));
        } catch (Exception e) {
            logger.error("wind风速风向解析出错,---{}", e);
            temp.clear();
        }
        return windHbaseVo;
    }


    // =====================旧的=====================

    /**
     * 判断数据是否同一秒，同一秒则只取其中一组
     *
     * @param kafkaMessage
     * @return
     */
    public static WindHbaseVo getWindList(KafkaMessage kafkaMessage) {
        WindHbaseVo windHbaseVo = null;

        List<String> temp = BaseAnalysis.getTempList(TEMP_KEY, kafkaMessage.getCode());
        if (temp == null) {
            temp = new ArrayList<>();
        }

        try {
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime();

            // 判断时间是否为同一秒
            boolean isSame = BaseAnalysis.isSameTime(TEMP_KEY, kafkaMessage.getCode(), currentTime);

            // 同一秒的数据不做发送，在下一秒后再一起合并
            if (!CollectionUtils.isEmpty(temp) && kafkaMessage.getMsg().startsWith("$WIMWV") && !isSame) {
                windHbaseVo = new WindHbaseVo();
                windHbaseVo.setInitialTime(DateUtils.fetchWholeSecond(currentTime).toString());
                for (String line : temp) {
                    translateLine(windHbaseVo, line);
                }
                temp.clear();
            }

            temp.add(kafkaMessage.getMsg());

            BaseAnalysis.updateTemp(TEMP_KEY, kafkaMessage.getCode(), temp, currentTime);
        } catch (Exception e) {
            logger.error("wind风速风向解析出错,---{}", e);
            temp.clear();
        }
        return windHbaseVo;
    }

    /**
     * 解析预览
     *
     * @param kafkaMessage
     * @return
     */
    public static WindHbaseVo getParseWindList(List<KafkaMessage> kafkaMessage) {
        WindHbaseVo windHbaseVo = null;

        List<String> temp = new ArrayList<>();
        for (KafkaMessage kafkaMessage1:
                kafkaMessage) {
            if (kafkaMessage1.getMsg().startsWith("$WIMWV")){
                temp.add(kafkaMessage1.getMsg());
            }

        }

        try {
//            long currentTime = kafkaMessage.get(0).getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.get(0).getInitialTime();

            // 判断时间是否为同一秒
//            boolean isSame = BaseAnalysis.isSameTime(TEMP_KEY, kafkaMessage.getCode(), currentTime);

            // 同一秒的数据不做发送，在下一秒后再一起合并
//            if (!CollectionUtils.isEmpty(temp) && kafkaMessage.getMsg().startsWith("$WIMWV")) {
                windHbaseVo = new WindHbaseVo();
//                windHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(currentTime,"yyyy-MM-dd HH:mm:ss").toString());
                for (String line : temp) {
                    translateLine(windHbaseVo, line);
                }
            if (AwsAnalysis.checkObjAllFieldsIsNull(windHbaseVo)){
                windHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss").toString());
                windHbaseVo.setInitialTime(null);
            }else{
                return null;
            }
                temp.clear();
//            }

//            temp.add(kafkaMessage.getMsg());

//            BaseAnalysis.updateTemp(TEMP_KEY, kafkaMessage.getCode(), temp, currentTime);
        } catch (Exception e) {
            logger.error("wind风速风向解析出错,---{}", e);
            temp.clear();
        }
        return windHbaseVo;
    }

    /**
     * 转换一行数据
     *
     * @param line
     * @return
     */
    private static void translateLine(WindHbaseVo windHbaseVo, String line) {

        String prefix = line.substring(0, 6);
        switch (prefix) {
            case "$WIMWV": {
                Iimwv iimwv = new Iimwv();
                iimwv.dataAnalysis(line);

                if (iimwv.getRelativeWind() != null) {
                    windHbaseVo.setWindLogoR(iimwv.getWindLogoR());
                    windHbaseVo.setRelativeWind(iimwv.getRelativeWind());
                    windHbaseVo.setRelativeWindSpeed(iimwv.getRelativeWindSpeed());
                }
                if (iimwv.getTrueWind() != null) {
                    windHbaseVo.setWindLogoT(iimwv.getWindLogoT());
                    windHbaseVo.setTrueWind(iimwv.getTrueWind());
                    windHbaseVo.setTrueWindSpeed(iimwv.getTrueWindSpeed());
                }
            }
            break;

            default: {
            }
        }
    }
}
