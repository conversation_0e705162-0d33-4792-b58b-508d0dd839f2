//package com.snct.kafka;
//
//import com.alibaba.fastjson2.JSONObject;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.kafka.core.KafkaTemplate;
//import org.springframework.kafka.support.SendResult;
//import org.springframework.stereotype.Service;
//import org.springframework.util.concurrent.ListenableFuture;
//
//
///**
// * TCP消息存储处理服务类
// *
// * <AUTHOR>
// */
//@Service
//public class KafkaServicebak {
//    public final static Logger logger = LoggerFactory.getLogger(KafkaServicebak.class);
//
//    @Autowired
//    private KafkaTemplate<String, String> kafkaTemplate;
//
//    //设备（猫/功放/PDU）数据传输到云端
//    private static String NET_DEVICE_SOURCE_DATA_TOPIC = "snct_device_source_data_topic";
//
//    /**
//     * 发送数据到kafka
//     *
//     * @param message
//     */
//    public void send2Kafka(KafkaMessage message) {
//        //发送消息，topic不存在将自动创建新的topic
//        ListenableFuture<SendResult<String, String>> listenableFuture = kafkaTemplate.send(NET_DEVICE_SOURCE_DATA_TOPIC, JSONObject.toJSONString(message));
//        //添加成功发送消息的回调和失败的回调
//        listenableFuture.addCallback(
//                result -> {
//                },
//                ex -> logger.info("send message to {} failure,error message:{}", NET_DEVICE_SOURCE_DATA_TOPIC, ex.getMessage()));
//    }
//
//}
