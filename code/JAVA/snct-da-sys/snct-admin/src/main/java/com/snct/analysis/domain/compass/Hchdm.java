package com.snct.analysis.domain.compass;

import com.snct.system.domain.msg.Instrument;

/**
 * @description: compass的Hchdm数据
 * Example:$HCHDM,193.8,T*2C
 * $HEHDT,1,2
 * 逗号隔开
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Hchdm extends Instrument {
    /**
     * 1.当前罗经数据
     */
    private String compassData;
    /**
     * 2.数据真实+校驗碼
     */
    private String dataTrue;

    @Override
    public void dataAnalysis(String dataStr) {
        String[] values = dataStr.split(",", -1);
        values = super.valuesTrim(values);
        compassData = values[1];
        dataTrue = values[2];
    }

    public String getCompassData() {
        return compassData;
    }

    public void setCompassData(String compassData) {
        this.compassData = compassData;
    }

    public String getDataTrue() {
        return dataTrue;
    }

    public void setDataTrue(String dataTrue) {
        this.dataTrue = dataTrue;
    }
}
