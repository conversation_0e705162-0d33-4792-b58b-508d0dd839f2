package com.snct.analysis.enums;

public enum DeviceTypeEnum implements IEnum {
    GPS(32, "gps", "GPS"),
    ATTITUDE(33, "attitude", "姿态仪"),
    SEA_PATH(34, "sea_path", "声学GPS"),
    EA600(35, "ea600", "水深"),
    WINCH(36, "winch", "绞车"),
    SBE21(37, "sbe21", "走航SBE21"),
    AWS(38, "aws", "气象站"),
    GO8050(39, "co2", "走航CO2"),
    LOG(40, "log", "计程仪"),
    ECHO(41, "echo", "测深（浅水）"),
    COMPASS(42, "compass", "罗经(磁罗经和电罗经)"),
    DGPS1(43, "dgps1", "船载GPS1"),
    DGPS2(44, "dgps2", "船载GPS2"),
    AIS(45, "ais", "船载AIS"),
    WIND(46, "wind", "船载风速风向"),
    RA_CDU1(47, "racdu1", "手动舵1"),
    RA_CDU2(48, "racdu2", "手动舵2"),
    AUTOPILOT(49, "autopilot", "自动舵"),
    GPS_IN(53, "gps_in", "内置GPS"),
    COMPASS_IN(54, "compass_in", "内置罗经");

    private Integer value;
    private String alias;
    private String remark;

    private DeviceTypeEnum(int value, String alias, String remark) {
        this.value = value;
        this.alias = alias;
        this.remark = remark;
    }

    public static DeviceTypeEnum getByValue(int value) {
        for(DeviceTypeEnum deviceTypeEnum : values()) {
            if (deviceTypeEnum.getValue().equals(value)) {
                return deviceTypeEnum;
            }
        }

        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getAlias() {
        return this.alias;
    }

    public String getRemark() {
        return this.remark;
    }
}