package com.snct.web.controller.business;

import com.alibaba.fastjson2.JSONObject;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.core.page.TableDataInfo;
import com.snct.common.enums.BusinessType;
import com.snct.common.enums.KafkaMsgType;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.device.service.ModemDeviceService;
import com.snct.device.service.PduDeviceService;
import com.snct.kafka.KafkaMessage;
import com.snct.kafka.KafkaService;
import com.snct.serialport.RtxtService2;
import com.snct.system.domain.Device;
import com.snct.system.service.IDeviceService;
import com.snct.system.service.IShipService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 设备Controller
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@RestController
@RequestMapping("/business/device")
public class DeviceController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(DeviceController.class);
    
    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private IShipService shipService;

    @Autowired
    private RtxtService2 rtxtService2;

    @Autowired
    private PduDeviceService pduDeviceService;

    @Autowired
    private ModemDeviceService modemDeviceService;

    /**
     * 查询设备列表
     */
    @PreAuthorize("@ss.hasPermi('business:device:list')")
    @GetMapping("/list")
    public TableDataInfo list(Device device)
    {
        startPage();
        List<Device> list = deviceService.selectDeviceList(device);
        return getDataTable(list);
    }

    /**
     * 导出设备列表
     */
    @PreAuthorize("@ss.hasPermi('business:device:export')")
    @Log(title = "设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Device device)
    {
        List<Device> list = deviceService.selectDeviceList(device);
        ExcelUtil<Device> util = new ExcelUtil<Device>(Device.class);
        util.exportExcel(response, list, "设备数据");
    }

    /**
     * 获取设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:device:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(deviceService.selectDeviceById(id));
    }

    /**
     * 新增设备
     */
    @PreAuthorize("@ss.hasPermi('business:device:add')")
    @Log(title = "设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Device device)
    {
        return toAjax(deviceService.insertDevice(device));
    }

    /**
     * 修改设备
     */
    @PreAuthorize("@ss.hasPermi('business:device:edit')")
    @Log(title = "设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Device device)
    {
        try {
            // 获取修改前的设备配置
            Device oldDevice = deviceService.selectDeviceById(device.getId());

            // 更新设备配置
            int result = deviceService.updateDevice(device);

            if (result > 0) {
                // 检查是否需要重新加载串口配置
                if (needReloadSerialConfig(oldDevice, device)) {
                    logger.info("设备[{}]的串口相关配置发生变化，重新加载串口服务", device.getCode());
                    try {
                        // 重新加载串口配置
                        rtxtService2.reloadDeviceConfig();
                        logger.info("设备[{}]串口配置重新加载成功", device.getCode());
                    } catch (Exception e) {
                        logger.error("设备[{}]串口配置重新加载失败", device.getCode(), e);
                    }
                }

                // 检查是否需要重新加载PDU设备配置
                if (needReloadPduConfig(oldDevice, device)) {
                    logger.info("设备[{}]的PDU相关配置发生变化，重新加载PDU连接", device.getCode());
                    try {
                        // 重新初始化PDU设备连接
                        pduDeviceService.reloadPduDevices();
                        logger.info("设备[{}]PDU配置重新加载成功", device.getCode());
                    } catch (Exception e) {
                        logger.error("设备[{}]PDU配置重新加载失败", device.getCode(), e);
                    }
                }

                // 检查是否需要重新加载Modem设备配置（Modem 不需要）
            }

            return toAjax(result);
        } catch (Exception e) {
            logger.error("修改设备配置失败", e);
            return error("修改设备配置失败：" + e.getMessage());
        }
    }

    /**
     * 启用设备
     */
    @PreAuthorize("@ss.hasPermi('business:device:edit')")
    @Log(title = "启用设备", businessType = BusinessType.UPDATE)
    @PostMapping("/enable/{id}")
    public AjaxResult enableDevice(@PathVariable Long id)
    {
        try {
            // 获取设备信息
            Device device = deviceService.selectDeviceById(id);
            if (device == null) {
                return error("设备不存在");
            }

            // 检查设备是否已经启用
            if (device.getEnable() != null && device.getEnable() == 1) {
                return success("设备已经是启用状态");
            }

            // 记录原始状态
            Integer oldEnable = device.getEnable();

            // 启用设备
            device.setEnable(1);
            int result = deviceService.updateDevice(device);

            if (result > 0) {
                logger.info("设备[{}]已启用，状态变更: {} -> {}", device.getCode(), oldEnable, 1);

                // 根据设备类型重新加载相应的服务
                reloadDeviceServiceByType(device);

                return success("设备启用成功");
            } else {
                return error("设备启用失败");
            }
        } catch (Exception e) {
            logger.error("启用设备失败", e);
            return error("启用设备失败：" + e.getMessage());
        }
    }

    /**
     * 禁用设备
     */
    @PreAuthorize("@ss.hasPermi('business:device:edit')")
    @Log(title = "禁用设备", businessType = BusinessType.UPDATE)
    @PostMapping("/disable/{id}")
    public AjaxResult disableDevice(@PathVariable Long id)
    {
        try {
            // 获取设备信息
            Device device = deviceService.selectDeviceById(id);
            if (device == null) {
                return error("设备不存在");
            }

            // 检查设备是否已经禁用
            if (device.getEnable() != null && device.getEnable() == 0) {
                return success("设备已经是禁用状态");
            }

            // 记录原始状态
            Integer oldEnable = device.getEnable();

            // 禁用设备
            device.setEnable(0);
            int result = deviceService.updateDevice(device);

            if (result > 0) {
                logger.info("设备[{}]已禁用，状态变更: {} -> {}", device.getCode(), oldEnable, 0);

                // 根据设备类型重新加载相应的服务
                reloadDeviceServiceByType(device);

                return success("设备禁用成功");
            } else {
                return error("设备禁用失败");
            }
        } catch (Exception e) {
            logger.error("禁用设备失败", e);
            return error("禁用设备失败：" + e.getMessage());
        }
    }

    /**
     * 删除设备
     */
    @PreAuthorize("@ss.hasPermi('business:device:remove')")
    @Log(title = "设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(deviceService.deleteDeviceByIds(ids));
    }

    /**
     * 同步设备消息
     */
    @PreAuthorize("@ss.hasPermi('business:device:edit')")
    @Log(title = "设备", businessType = BusinessType.OTHER)
    @PostMapping("/sync")
    public AjaxResult syncDeviceMessage()
    {
        try {
            List<Device> devices = deviceService.selectDeviceList(new Device());

            if (devices != null && !devices.isEmpty()) {
                KafkaMessage msg = new KafkaMessage();
                msg.setSn(devices.get(0).getSn());
                msg.setType(KafkaMsgType.SYC.ordinal());
                msg.setCode("DEVICE_DATA");
                msg.setMsg(JSONObject.toJSONString(devices));
                kafkaService.send2Kafka(msg);
                logger.info("同步设备消息成功，共{}条数据", devices.size());
                return success("同步设备消息成功");
            } else {
                logger.warn("没有获取到设备数据");
                return error("没有获取到设备数据");
            }
        } catch (Exception e) {
            logger.error("同步设备消息失败", e);
            return error("同步设备消息失败：" + e.getMessage());
        }
    }


    public static Map<String,String> viewKey = new ConcurrentHashMap<String, String>();
    public static Map<String,String> viewValue = new ConcurrentHashMap<String,String>();
    /**
     * 同步设备消息
     */
    //@Log(title = "刷新设备数据", businessType = BusinessType.OTHER)
    @GetMapping("/refreshdata")
    public AjaxResult refreshdata(HttpServletRequest request)
    {
        String deviceid =  request.getParameter("deviceid");
        String code =   request.getParameter("code");
//        DeviceController.viewKey.clear();
//        DeviceController.viewValue.clear();
        DeviceController.viewKey.put(code,"1");
        Map<String,String> viewMap = DeviceController.viewValue;
        List<String> dkey = new ArrayList<String>();
        String re = "";
        for (Map.Entry<String, String> entry : viewMap.entrySet()) {
            System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
            re = "<div>"+entry.getValue()+"</div>" + re;
            dkey.add(entry.getKey());
        }
        for(String str : dkey){
            DeviceController.viewValue.remove(str);
        }
        return success(re+"###"+deviceid);
    }

    @GetMapping("/refreshdataclose")
    public AjaxResult refreshdataclose(HttpServletRequest request) throws InterruptedException {
        System.out.println("#####refreshdataclose####");
        DeviceController.viewKey.clear();
        Thread.sleep(500l);
        DeviceController.viewValue.clear();
        return success();
    }

    /**
     * 根据设备连接方式重新加载相应的服务
     */
    private void reloadDeviceServiceByType(Device device) {
        if (device == null) {
            return;
        }

        try {
            Integer connectType = device.getConnectType();

            if (connectType == null) {
                logger.warn("设备[{}]连接方式为空，无法确定重载策略", device.getCode());
                return;
            }

            switch (connectType) {
                case 1: // 串口连接
                    if (device.getSerialPort() != null && !device.getSerialPort().trim().isEmpty()) {
                        logger.info("重新加载串口设备[{}]服务，串口: {}", device.getCode(), device.getSerialPort());
                        rtxtService2.reloadDeviceConfig();
                    } else {
                        logger.warn("串口设备[{}]串口号为空，跳过重载", device.getCode());
                    }
                    break;

                case 2: // TCP连接
                    if (device.getIp() != null && !device.getIp().trim().isEmpty() && device.getPort() != null) {
                        logger.info("重新加载TCP设备[{}]服务，地址: {}:{}", device.getCode(), device.getIp(), device.getPort());
                        pduDeviceService.reloadPduDevices();
                    } else {
                        logger.warn("TCP设备[{}]网络配置不完整，跳过重载", device.getCode());
                    }
                    break;
            }
        } catch (Exception e) {
            logger.error("重新加载设备[{}]服务失败", device.getCode(), e);
        }
    }

    /**
     * 检查是否需要重新加载串口配置
     * 当串口相关的关键配置发生变化时返回true
     */
    private boolean needReloadSerialConfig(Device oldDevice, Device newDevice) {
        if (oldDevice == null || newDevice == null) {
            return false;
        }

        // 检查设备编号是否变化
        if (!java.util.Objects.equals(oldDevice.getCode(), newDevice.getCode())) {
            logger.debug("设备[{}]编号发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getCode(), newDevice.getCode());
            return true;
        }

        // 检查串口号是否变化
        if (!java.util.Objects.equals(oldDevice.getSerialPort(), newDevice.getSerialPort())) {
            logger.debug("设备[{}]串口号发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getSerialPort(), newDevice.getSerialPort());
            return true;
        }

        // 检查波特率是否变化
        if (!java.util.Objects.equals(oldDevice.getBaudRate(), newDevice.getBaudRate())) {
            logger.debug("设备[{}]波特率发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getBaudRate(), newDevice.getBaudRate());
            return true;
        }

        // 检查数据位是否变化
        if (!java.util.Objects.equals(oldDevice.getDataBits(), newDevice.getDataBits())) {
            logger.debug("设备[{}]数据位发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getDataBits(), newDevice.getDataBits());
            return true;
        }

        // 检查停止位是否变化
        if (!java.util.Objects.equals(oldDevice.getStopBits(), newDevice.getStopBits())) {
            logger.debug("设备[{}]停止位发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getStopBits(), newDevice.getStopBits());
            return true;
        }

        // 检查校验位是否变化
        if (!java.util.Objects.equals(oldDevice.getParity(), newDevice.getParity())) {
            logger.debug("设备[{}]校验位发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getParity(), newDevice.getParity());
            return true;
        }

        // 检查启用状态是否变化
        if (!java.util.Objects.equals(oldDevice.getEnable(), newDevice.getEnable())) {
            logger.debug("设备[{}]启用状态发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getEnable(), newDevice.getEnable());
            return true;
        }

        // 检查传输间隔是否变化
        if (!java.util.Objects.equals(oldDevice.getCompartment(), newDevice.getCompartment())) {
            logger.debug("设备[{}]传输间隔发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getCompartment(), newDevice.getCompartment());
            return true;
        }

        // 检查消息长度限制是否变化
        if (!java.util.Objects.equals(oldDevice.getMsgLength(), newDevice.getMsgLength())) {
            logger.debug("设备[{}]消息长度限制发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getMsgLength(), newDevice.getMsgLength());
            return true;
        }

        return false;
    }

    /**
     * 检查是否需要重新加载PDU设备配置
     * 当PDU设备的网络相关配置发生变化时返回true
     */
    private boolean needReloadPduConfig(Device oldDevice, Device newDevice) {
        if (oldDevice == null || newDevice == null) {
            return false;
        }

        // 只处理PDU设备（类型51）
        if (newDevice.getType() == null || newDevice.getType() != 51L) {
            return false;
        }

        // 检查IP地址是否变化
        if (!java.util.Objects.equals(oldDevice.getIp(), newDevice.getIp())) {
            logger.debug("PDU设备[{}]IP地址发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getIp(), newDevice.getIp());
            return true;
        }

        // 检查端口是否变化
        if (!java.util.Objects.equals(oldDevice.getPort(), newDevice.getPort())) {
            logger.debug("PDU设备[{}]端口发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getPort(), newDevice.getPort());
            return true;
        }

        // 检查启用状态是否变化
        if (!java.util.Objects.equals(oldDevice.getEnable(), newDevice.getEnable())) {
            logger.debug("PDU设备[{}]启用状态发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getEnable(), newDevice.getEnable());
            return true;
        }

        return false;
    }

}
