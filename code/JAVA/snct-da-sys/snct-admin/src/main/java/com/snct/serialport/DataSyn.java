package com.snct.serialport;

import com.alibaba.fastjson2.JSONObject;
import com.snct.common.core.redis.RedisCache;
import com.snct.common.enums.KafkaMsgType;
import com.snct.kafka.KafkaMessage;
import com.snct.system.domain.Device;
import com.snct.system.domain.Ship;
import com.snct.system.service.IDeviceService;
import com.snct.system.service.IShipService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Component
public class DataSyn {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private IShipService shipService;

    // 0表示船舶信息未配置   1表示船舶已配置，设备信息未配置  100表示船舶与设备信息未同步 101表示船舶信息未同步 110设备信息未同步
    // 111表示同步完成
    private static final String DATA_SYN = "dsyn";
    public  Integer isSyn() {
        String dsyn = redisCache.getCacheObject(DATA_SYN);
        if( !StringUtils.isNotBlank(dsyn) && Integer.parseInt(dsyn) == 1) {
            return 111;
        }else{
            List<Ship> shipsList = shipService.selectAllShipList();
            if(shipsList.size()== 0) {
                return 0;
            }
            List<Device> DeviceList = deviceService.selectAllDevice();
            if(DeviceList.size()== 0) {
                return 1;
            }
            int re = 0;
            Ship ship = shipsList.get(0);
            if(ship.getStatus()!=2){
                re = 101;
            }
            for(Device device : DeviceList){
                if( device.getEnable() != 2){
                    if(re==101){
                        re = 100;
                    }else{
                        re = 110;
                    }
                }
            }
            return re;

        }
    }

    //获取需要同步的船舶数据
    public KafkaMessage getShipData() {

        try {
            List<Ship> ships = shipService.selectAllShipList();
            if (ships != null && !ships.isEmpty()) {
                Ship ship = ships.get(0);
                if(ship.getStatus()!=1){
                    //未启用  或已经同步
                    return null;
                }
                KafkaMessage msg = new KafkaMessage();
                msg.setType(KafkaMsgType.SYC.ordinal());
                msg.setCode("SHIP_DATA");
                msg.setMsg(JSONObject.toJSONString(ship));
                return msg;
            } else {
                //logger.warn("没有获取到船舶数据");
                //无数据，请先配置
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    //获取需要同步的设备数据
    public List<KafkaMessage> getDeviceData() {

        List<KafkaMessage> reList = new ArrayList<KafkaMessage>();
        try {
            List<Device> devicesList = deviceService.selectAllDevice();
            if (devicesList != null && !devicesList.isEmpty()) {
                for(Device device : devicesList){
                    if(device.getEnable() == 1){   //已启用未同步  系统自动同步到云服务器
                        KafkaMessage msg = new KafkaMessage();
                        msg.setSn(device.getSn());
                        msg.setType(KafkaMsgType.SYC.ordinal());
                        msg.setCode("DEVICE_DATA");
                        msg.setMsg(JSONObject.toJSONString(device));
                        reList.add(msg);
                    }
                }
            } else {
                //logger.warn("没有获取到设备数据");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return reList;
    }

}
