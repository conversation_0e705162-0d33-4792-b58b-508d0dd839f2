package com.snct.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.snct.system.domain.msg.BuMsgPduOut;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.enums.BusinessType;
import com.snct.system.service.IBuMsgPduOutService;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.common.core.page.TableDataInfo;

/**
 * pdu-out消息Controller
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
@RestController
@RequestMapping("/system/out")
public class BuMsgPduOutController extends BaseController
{
    @Autowired
    private IBuMsgPduOutService buMsgPduOutService;

    /**
     * 查询pdu-out消息列表
     */
    @PreAuthorize("@ss.hasPermi('system:out:list')")
    @GetMapping("/list")
    public TableDataInfo list(BuMsgPduOut buMsgPduOut)
    {
        startPage();
        List<BuMsgPduOut> list = buMsgPduOutService.selectBuMsgPduOutList(buMsgPduOut);
        return getDataTable(list);
    }

    /**
     * 导出pdu-out消息列表
     */
    @PreAuthorize("@ss.hasPermi('system:out:export')")
    @Log(title = "pdu-out消息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BuMsgPduOut buMsgPduOut)
    {
        List<BuMsgPduOut> list = buMsgPduOutService.selectBuMsgPduOutList(buMsgPduOut);
        ExcelUtil<BuMsgPduOut> util = new ExcelUtil<BuMsgPduOut>(BuMsgPduOut.class);
        util.exportExcel(response, list, "pdu-out消息数据");
    }

    /**
     * 获取pdu-out消息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:out:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(buMsgPduOutService.selectBuMsgPduOutById(id));
    }

    /**
     * 新增pdu-out消息
     */
    @PreAuthorize("@ss.hasPermi('system:out:add')")
    @Log(title = "pdu-out消息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BuMsgPduOut buMsgPduOut)
    {
        return toAjax(buMsgPduOutService.insertBuMsgPduOut(buMsgPduOut));
    }

    /**
     * 修改pdu-out消息
     */
    @PreAuthorize("@ss.hasPermi('system:out:edit')")
    @Log(title = "pdu-out消息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BuMsgPduOut buMsgPduOut)
    {
        return toAjax(buMsgPduOutService.updateBuMsgPduOut(buMsgPduOut));
    }

    /**
     * 删除pdu-out消息
     */
    @PreAuthorize("@ss.hasPermi('system:out:remove')")
    @Log(title = "pdu-out消息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(buMsgPduOutService.deleteBuMsgPduOutByIds(ids));
    }
}
