package com.snct.analysis.domain.compass;

import com.snct.analysis.vo.CompassHbaseVo;

import java.util.List;

public class CompassVo {
    private CompassHbaseVo compassHbaseVo;
    private List<Hproperty> hproperty;

    public CompassHbaseVo getCompassHbaseVo() {
        return compassHbaseVo;
    }

    public void setCompassHbaseVo(CompassHbaseVo compassHbaseVo) {
        this.compassHbaseVo = compassHbaseVo;
    }

    public List<Hproperty> getHproperty() {
        return hproperty;
    }

    public void setHproperty(List<Hproperty> hproperty) {
        this.hproperty = hproperty;
    }
}
