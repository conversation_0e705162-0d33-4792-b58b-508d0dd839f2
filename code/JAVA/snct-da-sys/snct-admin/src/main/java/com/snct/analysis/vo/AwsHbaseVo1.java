package com.snct.analysis.vo;

import com.snct.system.domain.msg.Instrument;

public class AwsHbaseVo1 extends Instrument {
    private String initialTime;
    private String initialBjTime;
    private String relativeWind;
    private String windLogoR;
    private String relativeWindSpeed;
    private String trueWind;
    private String trueWindSpeed;
    private String windLogoT;
    private String windSpeedUnit;
    private String airTemType;
    private String airTemperature;
    private String airUnit;
    private String airSensor;
    private String humidityType;
    private String humidity;
    private String humidityUnit;
    private String humiditySensor;
    private String pointTemType;
    private String pointTem;
    private String pointTemSensor;
    private String pressureType;
    private String pressure;
    private String pressureUnit;
    private String pressureSensor;
    private String qfeType;
    private String qfe;
    private String qfeUnit;
    private String qfeId;
    private String qnhType;
    private String qnh;
    private String qnhUnit;
    private String qnhId;
    private String dpType;
    private String dp;
    private String dpUnit;
    private String dpId;

    public String getInitialTime() {
        return this.initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return this.initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getRelativeWind() {
        return this.relativeWind;
    }

    public void setRelativeWind(String relativeWind) {
        this.relativeWind = relativeWind;
    }

    public String getWindLogoR() {
        return this.windLogoR;
    }

    public void setWindLogoR(String windLogoR) {
        this.windLogoR = windLogoR;
    }

    public String getRelativeWindSpeed() {
        return this.relativeWindSpeed;
    }

    public void setRelativeWindSpeed(String relativeWindSpeed) {
        this.relativeWindSpeed = relativeWindSpeed;
    }

    public String getTrueWind() {
        return this.trueWind;
    }

    public void setTrueWind(String trueWind) {
        this.trueWind = trueWind;
    }

    public String getTrueWindSpeed() {
        return this.trueWindSpeed;
    }

    public void setTrueWindSpeed(String trueWindSpeed) {
        this.trueWindSpeed = trueWindSpeed;
    }

    public String getWindLogoT() {
        return this.windLogoT;
    }

    public void setWindLogoT(String windLogoT) {
        this.windLogoT = windLogoT;
    }

    public String getWindSpeedUnit() {
        return this.windSpeedUnit;
    }

    public void setWindSpeedUnit(String windSpeedUnit) {
        this.windSpeedUnit = windSpeedUnit;
    }

    public String getAirTemType() {
        return this.airTemType;
    }

    public void setAirTemType(String airTemType) {
        this.airTemType = airTemType;
    }

    public String getAirTemperature() {
        return this.airTemperature;
    }

    public void setAirTemperature(String airTemperature) {
        this.airTemperature = airTemperature;
    }

    public String getAirUnit() {
        return this.airUnit;
    }

    public void setAirUnit(String airUnit) {
        this.airUnit = airUnit;
    }

    public String getAirSensor() {
        return this.airSensor;
    }

    public void setAirSensor(String airSensor) {
        this.airSensor = airSensor;
    }

    public String getHumidityType() {
        return this.humidityType;
    }

    public void setHumidityType(String humidityType) {
        this.humidityType = humidityType;
    }

    public String getHumidity() {
        return this.humidity;
    }

    public void setHumidity(String humidity) {
        this.humidity = humidity;
    }

    public String getHumidityUnit() {
        return this.humidityUnit;
    }

    public void setHumidityUnit(String humidityUnit) {
        this.humidityUnit = humidityUnit;
    }

    public String getHumiditySensor() {
        return this.humiditySensor;
    }

    public void setHumiditySensor(String humiditySensor) {
        this.humiditySensor = humiditySensor;
    }

    public String getPointTemType() {
        return this.pointTemType;
    }

    public void setPointTemType(String pointTemType) {
        this.pointTemType = pointTemType;
    }

    public String getPointTem() {
        return this.pointTem;
    }

    public void setPointTem(String pointTem) {
        this.pointTem = pointTem;
    }

    public String getPointTemSensor() {
        return this.pointTemSensor;
    }

    public void setPointTemSensor(String pointTemSensor) {
        this.pointTemSensor = pointTemSensor;
    }

    public String getPressureType() {
        return this.pressureType;
    }

    public void setPressureType(String pressureType) {
        this.pressureType = pressureType;
    }

    public String getPressure() {
        return this.pressure;
    }

    public void setPressure(String pressure) {
        this.pressure = pressure;
    }

    public String getPressureUnit() {
        return this.pressureUnit;
    }

    public void setPressureUnit(String pressureUnit) {
        this.pressureUnit = pressureUnit;
    }

    public String getPressureSensor() {
        return this.pressureSensor;
    }

    public void setPressureSensor(String pressureSensor) {
        this.pressureSensor = pressureSensor;
    }

    public String getQfeType() {
        return this.qfeType;
    }

    public void setQfeType(String qfeType) {
        this.qfeType = qfeType;
    }

    public String getQfe() {
        return this.qfe;
    }

    public void setQfe(String qfe) {
        this.qfe = qfe;
    }

    public String getQfeUnit() {
        return this.qfeUnit;
    }

    public void setQfeUnit(String qfeUnit) {
        this.qfeUnit = qfeUnit;
    }

    public String getQfeId() {
        return this.qfeId;
    }

    public void setQfeId(String qfeId) {
        this.qfeId = qfeId;
    }

    public String getQnhType() {
        return this.qnhType;
    }

    public void setQnhType(String qnhType) {
        this.qnhType = qnhType;
    }

    public String getQnh() {
        return this.qnh;
    }

    public void setQnh(String qnh) {
        this.qnh = qnh;
    }

    public String getQnhUnit() {
        return this.qnhUnit;
    }

    public void setQnhUnit(String qnhUnit) {
        this.qnhUnit = qnhUnit;
    }

    public String getQnhId() {
        return this.qnhId;
    }

    public void setQnhId(String qnhId) {
        this.qnhId = qnhId;
    }

    public String getDpType() {
        return this.dpType;
    }

    public void setDpType(String dpType) {
        this.dpType = dpType;
    }

    public String getDp() {
        return this.dp;
    }

    public void setDp(String dp) {
        this.dp = dp;
    }

    public String getDpUnit() {
        return this.dpUnit;
    }

    public void setDpUnit(String dpUnit) {
        this.dpUnit = dpUnit;
    }

    public String getDpId() {
        return this.dpId;
    }

    public void setDpId(String dpId) {
        this.dpId = dpId;
    }

    @Override
    public void dataAnalysis(String dataStr) {
        String[] values = super.valuesTrim(dataStr.split(" ", -1));
        for (int j = 0; j < values.length; j++) {
            if (values[j].startsWith("V")) {
                setRelativeWindSpeed(values[j].substring(1, values[j].length()));
            } else if (values[j].startsWith("D")) {
                setRelativeWind(values[j].substring(1, values[j].length()));
            } else if (values[j].startsWith("T")) {
                setAirTemperature(values[j].substring(1, values[j].length()));
            } else if (values[j].startsWith("H")) {
                setHumidity(values[j].substring(1, values[j].length()));
            } else if (values[j].startsWith("F")) {
                setQfe(values[j].substring(1, values[j].length()));
            } else if (values[j].startsWith("N")) {
                setQnh(values[j].substring(1, values[j].length()));
            }
        }
    }

    @Override
    public String toString() {
        return "AwsHbaseVo1{" +
                "initialTime='" + initialTime + '\'' +
                ", initialBjTime='" + initialBjTime + '\'' +
                ", relativeWind='" + relativeWind + '\'' +
                ", windLogoR='" + windLogoR + '\'' +
                ", relativeWindSpeed='" + relativeWindSpeed + '\'' +
                ", trueWind='" + trueWind + '\'' +
                ", trueWindSpeed='" + trueWindSpeed + '\'' +
                ", windLogoT='" + windLogoT + '\'' +
                ", windSpeedUnit='" + windSpeedUnit + '\'' +
                ", airTemType='" + airTemType + '\'' +
                ", airTemperature='" + airTemperature + '\'' +
                ", airUnit='" + airUnit + '\'' +
                ", airSensor='" + airSensor + '\'' +
                ", humidityType='" + humidityType + '\'' +
                ", humidity='" + humidity + '\'' +
                ", humidityUnit='" + humidityUnit + '\'' +
                ", humiditySensor='" + humiditySensor + '\'' +
                ", pointTemType='" + pointTemType + '\'' +
                ", pointTem='" + pointTem + '\'' +
                ", pointTemSensor='" + pointTemSensor + '\'' +
                ", pressureType='" + pressureType + '\'' +
                ", pressure='" + pressure + '\'' +
                ", pressureUnit='" + pressureUnit + '\'' +
                ", pressureSensor='" + pressureSensor + '\'' +
                ", qfeType='" + qfeType + '\'' +
                ", qfe='" + qfe + '\'' +
                ", qfeUnit='" + qfeUnit + '\'' +
                ", qfeId='" + qfeId + '\'' +
                ", qnhType='" + qnhType + '\'' +
                ", qnh='" + qnh + '\'' +
                ", qnhUnit='" + qnhUnit + '\'' +
                ", qnhId='" + qnhId + '\'' +
                ", dpType='" + dpType + '\'' +
                ", dp='" + dp + '\'' +
                ", dpUnit='" + dpUnit + '\'' +
                ", dpId='" + dpId + '\'' +
                '}';
    }
}