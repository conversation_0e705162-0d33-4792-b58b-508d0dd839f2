<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuMsgPduMapper">
    
    <resultMap type="BuMsgPdu" id="BuMsgPduResult">
        <result property="id"    column="id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="batchCode"    column="batch_code"    />
        <result property="manage"    column="manage"    />
        <result property="electric"    column="electric"    />
        <result property="voltage"    column="voltage"    />
        <result property="yesPwoer"    column="yesPwoer"    />
        <result property="noPwoer"    column="noPwoer"    />
        <result property="seePwoer"    column="seePwoer"    />
        <result property="powerParam"    column="powerParam"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectBuMsgPduVo">
        select id, device_id, batch_code, manage, electric, voltage, yesPwoer, noPwoer, seePwoer, powerParam, status, create_time from bu_msg_pdu
    </sql>

    <select id="selectBuMsgPduList" parameterType="BuMsgPdu" resultMap="BuMsgPduResult">
        <include refid="selectBuMsgPduVo"/>
        <where>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
             <if test="batchCode != null "> and batch_code = #{batchCode}</if>
            <if test="manage != null "> and manage = #{manage}</if>
            <if test="electric != null "> and electric = #{electric}</if>
            <if test="voltage != null "> and voltage = #{voltage}</if>
            <if test="yesPwoer != null "> and yesPwoer = #{yesPwoer}</if>
            <if test="noPwoer != null "> and noPwoer = #{noPwoer}</if>
            <if test="seePwoer != null "> and seePwoer = #{seePwoer}</if>
            <if test="powerParam != null "> and powerParam = #{powerParam}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectBuMsgPduById" parameterType="Long" resultMap="BuMsgPduResult">
        <include refid="selectBuMsgPduVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuMsgPdu" parameterType="BuMsgPdu" useGeneratedKeys="true" keyProperty="id">
        insert into bu_msg_pdu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="batchCode != null">batch_code,</if>
            <if test="manage != null">manage,</if>
            <if test="electric != null">electric,</if>
            <if test="voltage != null">voltage,</if>
            <if test="yesPwoer != null">yesPwoer,</if>
            <if test="noPwoer != null">noPwoer,</if>
            <if test="seePwoer != null">seePwoer,</if>
            <if test="powerParam != null">powerParam,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="batchCode != null">#{batchCode},</if>
            <if test="manage != null">#{manage},</if>
            <if test="electric != null">#{electric},</if>
            <if test="voltage != null">#{voltage},</if>
            <if test="yesPwoer != null">#{yesPwoer},</if>
            <if test="noPwoer != null">#{noPwoer},</if>
            <if test="seePwoer != null">#{seePwoer},</if>
            <if test="powerParam != null">#{powerParam},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateBuMsgPdu" parameterType="BuMsgPdu">
        update bu_msg_pdu
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="batchCode != null">batch_code = #{batchCode},</if>
            <if test="manage != null">manage = #{manage},</if>
            <if test="electric != null">electric = #{electric},</if>
            <if test="voltage != null">voltage = #{voltage},</if>
            <if test="yesPwoer != null">yesPwoer = #{yesPwoer},</if>
            <if test="noPwoer != null">noPwoer = #{noPwoer},</if>
            <if test="seePwoer != null">seePwoer = #{seePwoer},</if>
            <if test="powerParam != null">powerParam = #{powerParam},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuMsgPduById" parameterType="Long">
        delete from bu_msg_pdu where id = #{id}
    </delete>

    <delete id="deleteBuMsgPduByIds" parameterType="String">
        delete from bu_msg_pdu where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>