<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.DataStatisticsMapper">

	<resultMap type="DataStatistics" id="DataStatisticsResult">
		<result property="id"    column="id"    />
		<result property="createTime"    column="create_time"    />
		<result property="characterLength"    column="character_length"    />
		<result property="deviceType"    column="device_type"    />
		<result property="dataType"    column="data_type"    />
	</resultMap>

	<sql id="selectDataStatisticsVo">
		select id, create_time, character_length, device_type, data_type from bu_data_statistics
	</sql>

	<select id="selectDataStatisticsList" parameterType="DataStatistics" resultMap="DataStatisticsResult">
		<include refid="selectDataStatisticsVo"/>
		<where>
			<if test="characterLength != null "> and character_length = #{characterLength}</if>
			<if test="deviceType != null "> and device_type = #{deviceType}</if>
			<if test="dataType != null "> and data_type = #{dataType}</if>
		</where>
	</select>

	<select id="selectDataStatisticsById" parameterType="Long" resultMap="DataStatisticsResult">
		<include refid="selectDataStatisticsVo"/>
		where id = #{id}
	</select>

	<insert id="insertDataStatistics" parameterType="DataStatistics" useGeneratedKeys="true" keyProperty="id">
		insert into bu_data_statistics
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="createTime != null">create_time,</if>
			<if test="characterLength != null">character_length,</if>
			<if test="deviceType != null">device_type,</if>
			<if test="dataType != null">data_type,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="createTime != null">#{createTime},</if>
			<if test="characterLength != null">#{characterLength},</if>
			<if test="deviceType != null">#{deviceType},</if>
			<if test="dataType != null">#{dataType},</if>
		</trim>
	</insert>

	<update id="updateDataStatistics" parameterType="DataStatistics">
		update bu_data_statistics
		<trim prefix="SET" suffixOverrides=",">
			<if test="createTime != null">create_time = #{createTime},</if>
			<if test="characterLength != null">character_length = #{characterLength},</if>
			<if test="deviceType != null">device_type = #{deviceType},</if>
			<if test="dataType != null">data_type = #{dataType},</if>
		</trim>
		where id = #{id}
	</update>

	<delete id="deleteDataStatisticsById" parameterType="Long">
		delete from bu_data_statistics where id = #{id}
	</delete>

	<delete id="deleteDataStatisticsByIds" parameterType="String">
		delete from bu_data_statistics where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>
</mapper>