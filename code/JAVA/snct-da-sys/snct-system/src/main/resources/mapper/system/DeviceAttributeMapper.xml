<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.DeviceAttributeMapper">

	<resultMap type="DeviceAttribute" id="DeviceAttributeResult">
		<result property="id"    column="id"    />
		<result property="type"    column="type"    />
		<result property="name"    column="name"    />
		<result property="label"    column="label"    />
		<result property="remark"    column="remark"    />
		<result property="createBy"    column="create_by"    />
		<result property="createTime"    column="create_time"    />
		<result property="updateBy"    column="update_by"    />
		<result property="updateTime"    column="update_time"    />
	</resultMap>

	<sql id="selectDeviceAttributeVo">
		select id, type, name, label, remark, create_by, create_time, update_by, update_time from bu_device_attribute
	</sql>

	<select id="selectDeviceAttributeList" parameterType="DeviceAttribute" resultMap="DeviceAttributeResult">
		<include refid="selectDeviceAttributeVo"/>
		<where>
			<if test="type != null "> and type = #{type}</if>
			<if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
			<if test="label != null  and label != ''"> and label = #{label}</if>
		</where>
	</select>

	<select id="selectDeviceAttributeById" parameterType="Long" resultMap="DeviceAttributeResult">
		<include refid="selectDeviceAttributeVo"/>
		where id = #{id}
	</select>

	<insert id="insertDeviceAttribute" parameterType="DeviceAttribute" useGeneratedKeys="true" keyProperty="id">
		insert into bu_device_attribute
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="type != null">type,</if>
			<if test="name != null">name,</if>
			<if test="label != null">label,</if>
			<if test="remark != null">remark,</if>
			<if test="createBy != null">create_by,</if>
			<if test="createTime != null">create_time,</if>
			<if test="updateBy != null">update_by,</if>
			<if test="updateTime != null">update_time,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="type != null">#{type},</if>
			<if test="name != null">#{name},</if>
			<if test="label != null">#{label},</if>
			<if test="remark != null">#{remark},</if>
			<if test="createBy != null">#{createBy},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="updateBy != null">#{updateBy},</if>
			<if test="updateTime != null">#{updateTime},</if>
		</trim>
	</insert>

	<update id="updateDeviceAttribute" parameterType="DeviceAttribute">
		update bu_device_attribute
		<trim prefix="SET" suffixOverrides=",">
			<if test="type != null">type = #{type},</if>
			<if test="name != null">name = #{name},</if>
			<if test="label != null">label = #{label},</if>
			<if test="remark != null">remark = #{remark},</if>
			<if test="createBy != null">create_by = #{createBy},</if>
			<if test="createTime != null">create_time = #{createTime},</if>
			<if test="updateBy != null">update_by = #{updateBy},</if>
			<if test="updateTime != null">update_time = #{updateTime},</if>
		</trim>
		where id = #{id}
	</update>

	<delete id="deleteDeviceAttributeById" parameterType="Long">
		delete from bu_device_attribute where id = #{id}
	</delete>

	<delete id="deleteDeviceAttributeByIds" parameterType="String">
		delete from bu_device_attribute where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>
</mapper>