package com.snct.system.service.impl;

import java.util.List;
import com.snct.common.utils.DateUtils;
import com.snct.system.domain.msg.BuMsgPdu;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuMsgPduMapper;
import com.snct.system.service.IBuMsgPduService;

/**
 * pdu消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
@Service
public class BuMsgPduServiceImpl implements IBuMsgPduService 
{
    @Autowired
    private BuMsgPduMapper buMsgPduMapper;

    /**
     * 查询pdu消息
     * 
     * @param id pdu消息主键
     * @return pdu消息
     */
    @Override
    public BuMsgPdu selectBuMsgPduById(Long id)
    {
        return buMsgPduMapper.selectBuMsgPduById(id);
    }

    /**
     * 查询pdu消息列表
     * 
     * @param buMsgPdu pdu消息
     * @return pdu消息
     */
    @Override
    public List<BuMsgPdu> selectBuMsgPduList(BuMsgPdu buMsgPdu)
    {
        return buMsgPduMapper.selectBuMsgPduList(buMsgPdu);
    }

    /**
     * 新增pdu消息
     * 
     * @param buMsgPdu pdu消息
     * @return 结果
     */
    @Override
    public int insertBuMsgPdu(BuMsgPdu buMsgPdu)
    {
        buMsgPdu.setCreateTime(DateUtils.getNowDate());
        return buMsgPduMapper.insertBuMsgPdu(buMsgPdu);
    }

    /**
     * 修改pdu消息
     * 
     * @param buMsgPdu pdu消息
     * @return 结果
     */
    @Override
    public int updateBuMsgPdu(BuMsgPdu buMsgPdu)
    {
        return buMsgPduMapper.updateBuMsgPdu(buMsgPdu);
    }

    /**
     * 批量删除pdu消息
     * 
     * @param ids 需要删除的pdu消息主键
     * @return 结果
     */
    @Override
    public int deleteBuMsgPduByIds(Long[] ids)
    {
        return buMsgPduMapper.deleteBuMsgPduByIds(ids);
    }

    /**
     * 删除pdu消息信息
     * 
     * @param id pdu消息主键
     * @return 结果
     */
    @Override
    public int deleteBuMsgPduById(Long id)
    {
        return buMsgPduMapper.deleteBuMsgPduById(id);
    }
}
