package com.snct.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 任务对象 sys_data_auth
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public class SysRestart extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    private Long restartId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String restartName;

    /** 任务编码 */
    @Excel(name = "任务类型")
    private Integer restartType;

    /** 那一天 */
    @Excel(name = "重启日期")
    private String today;

    /** 显示顺序 */
    @Excel(name = "重启时间")
    private String restartTime;

    /** 最近重启时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date doTime;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setRestartId(Long restartId)
    {
        this.restartId = restartId;
    }

    public Long getRestartId()
    {
        return restartId;
    }

    public void setRestartType(Integer restartType)
    {
        this.restartType = restartType;
    }

    public Integer getRestartType()
    {
        return restartType;
    }

    public void setToday(String today)
    {
        this.today = today;
    }

    public String getToday()
    {
        return today;
    }

    public void setRestartName(String restartName)
    {
        this.restartName = restartName;
    }

    public String getRestartName()
    {
        return restartName;
    }

    public void setRestartTime(String restartTime)
    {
        this.restartTime = restartTime;
    }

    public String getRestartTime()
    {
        return restartTime;
    }

    public Date getDoTime()
    {
        return doTime;
    }

    public void setDoTime(Date doTime)
    {
        this.doTime = doTime;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("restartId", getRestartId())
            .append("restartType", getRestartType())
            .append("restartName", getRestartName())
            .append("today", getToday())
            .append("restartTime", getRestartTime())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
