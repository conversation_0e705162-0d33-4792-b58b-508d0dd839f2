package com.snct.system.domain.msg.aws;

import com.snct.system.domain.msg.Instrument;

/**
 * 风速风向数据解析类
 * 
 * 用于解析NMEA $WIMWV格式数据
 * 示例: $WIMWV,270,R,1.7,M,A*3D
 * 
 * 字段含义:
 * 1. 风向角度 (0-360°)
 * 2. 参考 (R=相对, T=真实)
 * 3. 风速值
 * 4. 风速单位 (K=千米/小时, M=米/秒, N=海里/小时)
 * 5. 状态 (A=有效数据, V=无效数据)
 * 
 * <AUTHOR>
 * @date 2025-06-04
 */
public class Wimwv extends Instrument {
    private String relativeWind;
    private String WindLogoR;
    private String relativeWindSpeed;
    private String trueWind;
    private String trueWindSpeed;
    private String WindLogoT;
    private String windSpeedUnit;

    @Override
    public void dataAnalysis(String dataStr) {
        try {
            String[] values = super.valuesTrim(dataStr.split(",", -1));
            if ("R".equals(values[2])) {
                this.relativeWind = values[1];
                this.WindLogoR = values[2];
                this.relativeWindSpeed = values[3];
            } else if ("T".equals(values[2])) {
                this.trueWind = values[1];
                this.WindLogoT = values[2];
                this.trueWindSpeed = values[3];
            }
            this.windSpeedUnit = values[4];
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getRelativeWind() {
        return this.relativeWind;
    }

    public void setRelativeWind(String relativeWind) {
        this.relativeWind = relativeWind;
    }

    public String getWindLogoR() {
        return this.WindLogoR;
    }

    public void setWindLogoR(String windLogoR) {
        this.WindLogoR = windLogoR;
    }

    public String getRelativeWindSpeed() {
        return this.relativeWindSpeed;
    }

    public void setRelativeWindSpeed(String relativeWindSpeed) {
        this.relativeWindSpeed = relativeWindSpeed;
    }

    public String getTrueWind() {
        return this.trueWind;
    }

    public void setTrueWind(String trueWind) {
        this.trueWind = trueWind;
    }

    public String getTrueWindSpeed() {
        return this.trueWindSpeed;
    }

    public void setTrueWindSpeed(String trueWindSpeed) {
        this.trueWindSpeed = trueWindSpeed;
    }

    public String getWindLogoT() {
        return this.WindLogoT;
    }

    public void setWindLogoT(String windLogoT) {
        this.WindLogoT = windLogoT;
    }

    public String getWindSpeedUnit() {
        return this.windSpeedUnit;
    }

    public void setWindSpeedUnit(String windSpeedUnit) {
        this.windSpeedUnit = windSpeedUnit;
    }
}