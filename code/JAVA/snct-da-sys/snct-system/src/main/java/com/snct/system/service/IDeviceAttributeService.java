package com.snct.system.service;

import java.util.List;
import com.snct.system.domain.DeviceAttribute;

/**
 * 设备属性Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface IDeviceAttributeService 
{
    /**
     * 查询设备属性
     * 
     * @param id 设备属性主键
     * @return 设备属性
     */
    public DeviceAttribute selectDeviceAttributeById(Long id);

    /**
     * 查询设备属性列表
     * 
     * @param deviceAttribute 设备属性
     * @return 设备属性集合
     */
    public List<DeviceAttribute> selectDeviceAttributeList(DeviceAttribute deviceAttribute);

    /**
     * 新增设备属性
     * 
     * @param deviceAttribute 设备属性
     * @return 结果
     */
    public int insertDeviceAttribute(DeviceAttribute deviceAttribute);

    /**
     * 修改设备属性
     * 
     * @param deviceAttribute 设备属性
     * @return 结果
     */
    public int updateDeviceAttribute(DeviceAttribute deviceAttribute);

    /**
     * 批量删除设备属性
     * 
     * @param ids 需要删除的设备属性主键集合
     * @return 结果
     */
    public int deleteDeviceAttributeByIds(Long[] ids);

    /**
     * 删除设备属性信息
     * 
     * @param id 设备属性主键
     * @return 结果
     */
    public int deleteDeviceAttributeById(Long id);
}
