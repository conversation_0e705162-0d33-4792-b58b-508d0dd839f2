package com.snct.system.service.impl;

import java.util.List;
import com.snct.common.utils.DateUtils;
import com.snct.system.domain.msg.BuMsgModem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuMsgModemMapper;
import com.snct.system.service.IBuMsgModemService;

/**
 * Modem消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
@Service
public class BuMsgModemServiceImpl implements IBuMsgModemService 
{
    @Autowired
    private BuMsgModemMapper buMsgModemMapper;

    /**
     * 查询Modem消息
     * 
     * @param id Modem消息主键
     * @return Modem消息
     */
    @Override
    public BuMsgModem selectBuMsgModemById(Long id)
    {
        return buMsgModemMapper.selectBuMsgModemById(id);
    }

    /**
     * 查询Modem消息列表
     * 
     * @param buMsgModem Modem消息
     * @return Modem消息
     */
    @Override
    public List<BuMsgModem> selectBuMsgModemList(BuMsgModem buMsgModem)
    {
        return buMsgModemMapper.selectBuMsgModemList(buMsgModem);
    }

    /**
     * 新增Modem消息
     * 
     * @param buMsgModem Modem消息
     * @return 结果
     */
    @Override
    public int insertBuMsgModem(BuMsgModem buMsgModem)
    {
        buMsgModem.setCreateTime(DateUtils.getNowDate());
        return buMsgModemMapper.insertBuMsgModem(buMsgModem);
    }

    /**
     * 修改Modem消息
     * 
     * @param buMsgModem Modem消息
     * @return 结果
     */
    @Override
    public int updateBuMsgModem(BuMsgModem buMsgModem)
    {
        return buMsgModemMapper.updateBuMsgModem(buMsgModem);
    }

    /**
     * 批量删除Modem消息
     * 
     * @param ids 需要删除的Modem消息主键
     * @return 结果
     */
    @Override
    public int deleteBuMsgModemByIds(Long[] ids)
    {
        return buMsgModemMapper.deleteBuMsgModemByIds(ids);
    }

    /**
     * 删除Modem消息信息
     * 
     * @param id Modem消息主键
     * @return 结果
     */
    @Override
    public int deleteBuMsgModemById(Long id)
    {
        return buMsgModemMapper.deleteBuMsgModemById(id);
    }
}
