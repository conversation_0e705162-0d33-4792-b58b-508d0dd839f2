package com.snct.system.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.DefaultAttributeMapper;
import com.snct.system.domain.DefaultAttribute;
import com.snct.system.service.IDefaultAttributeService;

/**
 * 设备默认属性Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
@Service
public class DefaultAttributeServiceImpl implements IDefaultAttributeService 
{
    @Autowired
    private DefaultAttributeMapper defaultAttributeMapper;

    /**
     * 查询设备默认属性
     * 
     * @param id 设备默认属性主键
     * @return 设备默认属性
     */
    @Override
    public DefaultAttribute selectDefaultAttributeById(Long id)
    {
        return defaultAttributeMapper.selectDefaultAttributeById(id);
    }

    /**
     * 查询设备默认属性列表
     * 
     * @param defaultAttribute 设备默认属性
     * @return 设备默认属性
     */
    @Override
    public List<DefaultAttribute> selectDefaultAttributeList(DefaultAttribute defaultAttribute)
    {
        return defaultAttributeMapper.selectDefaultAttributeList(defaultAttribute);
    }

    /**
     * 新增设备默认属性
     * 
     * @param defaultAttribute 设备默认属性
     * @return 结果
     */
    @Override
    public int insertDefaultAttribute(DefaultAttribute defaultAttribute)
    {
        return defaultAttributeMapper.insertDefaultAttribute(defaultAttribute);
    }

    /**
     * 修改设备默认属性
     * 
     * @param defaultAttribute 设备默认属性
     * @return 结果
     */
    @Override
    public int updateDefaultAttribute(DefaultAttribute defaultAttribute)
    {
        return defaultAttributeMapper.updateDefaultAttribute(defaultAttribute);
    }

    /**
     * 批量删除设备默认属性
     * 
     * @param ids 需要删除的设备默认属性主键
     * @return 结果
     */
    @Override
    public int deleteDefaultAttributeByIds(Long[] ids)
    {
        return defaultAttributeMapper.deleteDefaultAttributeByIds(ids);
    }

    /**
     * 删除设备默认属性信息
     * 
     * @param id 设备默认属性主键
     * @return 结果
     */
    @Override
    public int deleteDefaultAttributeById(Long id)
    {
        return defaultAttributeMapper.deleteDefaultAttributeById(id);
    }

    @Override
    public List<String> selectDefaultAttributeStrs(Integer type) {
        try {
            // 参数校验
            if (type == null) {
                return Collections.emptyList();
            }

            DefaultAttribute defaultAttribute = new DefaultAttribute();
            defaultAttribute.setType(type);
            List<DefaultAttribute> list = selectDefaultAttributeList(defaultAttribute);

            // 空值检查
            if (list == null || list.isEmpty()) {
                return Collections.emptyList();
            }

            // 过滤空值并转换为字符串列表
            return list.stream()
                    .filter(v -> v != null && v.getName() != null && !v.getName().trim().isEmpty())
                    .map(v -> v.getName().trim())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            // 记录异常并返回空列表
            return Collections.emptyList();
        }
    }
}
