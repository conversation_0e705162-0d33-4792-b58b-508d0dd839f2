package com.snct.system.service;

import com.alibaba.fastjson2.JSONObject;
import com.snct.common.utils.IpUtil;
import com.snct.common.utils.ip.IpUtils;
import com.snct.system.domain.NetworkConfig;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网络配置服务类
 * <p>
 * 主要功能：
 * 1. 获取系统网卡配置信息
 * 2. 更新网卡配置（支持DHCP和静态IP配置）
 * 3. 管理网络连接（WAN/LAN配置）
 * 4. 读写网卡配置文件
 * 5. 网络状态检查和IP地址验证
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Service
public class NetworkService {
    private static final Logger logger = LoggerFactory.getLogger(NetworkService.class);

    // 常量定义
    private static final String MODE_DHCP = "DHCP";
    private static final String MODE_STATIC = "STATIC";
    private static final String TYPE_WAN = "WAN";
    private static final String TYPE_LAN = "LAN";

    private static final String IPV4_METHOD = "ipv4.method";
    private static final String IPV4_ADDRESS = "ipv4.address";
    private static final String IPV4_ADDRESSES = "ipv4.addresses";
    private static final String IPV4_GATEWAY = "ipv4.gateway";
    private static final String IPV4_DNS = "ipv4.dns";
    private static final String IPV4_ROUTES = "ipv4.routes";

    private static final long COMMAND_WAIT_TIME = 2000L;

    /**
     * 使用nmcli命令获取系统所有网卡配置信息
     * <p>
     * 执行流程：
     * 1. 通过Linux命令获取物理网卡列表（排除虚拟网卡）
     * 2. 遍历每个网卡，使用nmcli读取其配置信息
     * 3. 根据配置模式（DHCP/STATIC）获取相应的网络参数
     *
     * @return 网卡配置列表
     * @throws IOException IO异常
     */
    public List<NetworkConfig> getNetConfigByNmcli() throws IOException {
        List<NetworkConfig> networkConfigs = new ArrayList<>();

        try {
            // 获取物理网卡列表，排除虚拟网卡
            String[] command = {"/bin/sh", "-c", "ls /sys/class/net | grep -v `ls /sys/devices/virtual/net/`"};
            List<String> networkNames = executeCommand(command);

            logger.info("获取到的网卡列表: {}", JSONObject.toJSONString(networkNames));

            // 遍历每个网卡，获取详细配置信息
            for (String name : networkNames) {
                if (StringUtils.isNotEmpty(name)) {
                    NetworkConfig networkConfig = getNetworkConfigByNmcliSingle(name);
                    networkConfig.setName(name);
                    networkConfigs.add(networkConfig);
                }
            }
            return networkConfigs;
        } catch (Exception e) {
            logger.error("获取网卡配置失败", e);
            throw new IOException("获取网卡配置失败", e);
        }
    }

    /**
     * 更新网卡配置（使用nmcli命令方式）
     * <p>
     * 执行流程：
     * 1. 停止当前网络连接
     * 2. 删除现有连接配置
     * 3. 创建新的以太网连接
     * 4. 根据网卡类型（WAN/LAN）应用相应配置
     * 5. 启动网络连接
     *
     * @param networkConfig 网卡配置对象
     * @return 1-成功，0-失败
     */
    public synchronized int updateNetwork(NetworkConfig networkConfig) {
        String networkName = networkConfig.getName();

        try {
            // 1. 停止当前网络连接
            executeNmcliCommand("nmcli con down " + networkName);

            // 2. 删除现有连接配置
            executeNmcliCommand("nmcli con del " + networkName);

            // 3. 创建新的以太网连接
            executeNmcliCommand(String.format("nmcli con add con-name %s ifname %s type ethernet",
                    networkName, networkName));

            // 4. 根据网卡类型应用相应配置
            if (TYPE_WAN.equals(networkConfig.getType())) {
                exec4Wan(networkConfig);
            } else if (TYPE_LAN.equals(networkConfig.getType())) {
                exec4Lan(networkConfig);
            }

            // 5. 启动网络连接
            String upCommand = "nmcli con up " + networkName;
            logger.info("重启网络连接: {}", upCommand);
            executeNmcliCommand(upCommand);

            return 1;
        } catch (Exception e) {
            logger.error("配置网卡失败: {}", networkName, e);
            return 0;
        }
    }

    /**
     * 配置WAN口网络参数
     * <p>
     * WAN口特点：
     * - 支持DHCP自动获取IP
     * - 支持静态IP配置
     * - 可配置DNS服务器
     * - 通常用于连接外网
     *
     * @param networkConfig 网络配置对象
     * @throws Exception 配置异常
     */
    public void exec4Wan(NetworkConfig networkConfig) throws Exception {
        String networkName = networkConfig.getName();

        // DHCP模式：自动获取IP地址
        if (MODE_DHCP.equals(networkConfig.getMode())) {
            String dhcpCommand = String.format("nmcli con mod %s connection.autoconnect yes %s auto",
                    networkName, IPV4_METHOD);
            executeNmcliCommand(dhcpCommand);
            logger.info("配置DHCP模式: {}", networkName);
        }

        // 静态IP模式：手动配置IP、子网掩码、网关
        if (MODE_STATIC.equals(networkConfig.getMode())) {
            // 检查必要参数是否为空
            if (StringUtils.isEmpty(networkConfig.getIp()) || StringUtils.isEmpty(networkConfig.getNetmask())) {
                logger.warn("静态IP配置缺少必要参数，跳过配置: IP={}, 子网掩码={}",
                        networkConfig.getIp(), networkConfig.getNetmask());
                return;
            }

            StringBuilder commandBuilder = new StringBuilder();
            commandBuilder.append(String.format("nmcli con mod %s connection.autoconnect yes %s %s/%s",
                    networkName, IPV4_ADDRESS, networkConfig.getIp(), IpUtil.getMaskNum(networkConfig.getNetmask())));

            // 只有网关不为空时才添加网关配置
            if (StringUtils.isNotEmpty(networkConfig.getGateway())) {
                commandBuilder.append(String.format(" %s %s", IPV4_GATEWAY, networkConfig.getGateway()));
            }

            commandBuilder.append(String.format(" %s manual", IPV4_METHOD));

            String staticCommand = commandBuilder.toString();
            executeNmcliCommand(staticCommand);
            logger.info("配置静态IP模式: {}", networkName);
        }

        // 配置DNS服务器（支持主DNS和备用DNS）
        if (StringUtils.isNotEmpty(networkConfig.getDns1()) || StringUtils.isNotEmpty(networkConfig.getDns2())) {
            String dns1 = StringUtils.isEmpty(networkConfig.getDns1()) ? "" : networkConfig.getDns1();
            String dns2 = StringUtils.isEmpty(networkConfig.getDns2()) ? "" : networkConfig.getDns2();
            String dnsServers = (dns1 + " " + dns2).trim();

            String dnsCommand = String.format("nmcli con mod %s %s \"%s\"",
                    networkName, IPV4_DNS, dnsServers);
            String[] command = {"/bin/sh", "-c", dnsCommand};

            Runtime.getRuntime().exec(command);
            Thread.sleep(COMMAND_WAIT_TIME);
            logger.info("配置DNS服务器: {}", dnsServers);
        }
    }

    /**
     * 配置LAN口网络参数
     * <p>
     * LAN口特点：
     * - 仅支持静态IP配置
     * - 可配置多个访问路由（最多3个）
     * - 通常用于内网连接
     * - 不配置DNS服务器
     *
     * @param networkConfig 网络配置对象
     * @throws Exception 配置异常
     */
    public void exec4Lan(NetworkConfig networkConfig) throws Exception {
        // LAN口仅支持静态IP模式
        if (!MODE_STATIC.equals(networkConfig.getMode())) {
            return;
        }

        String networkName = networkConfig.getName();
        String gateway = networkConfig.getGateway();

        // 检查必要参数是否为空
        if (StringUtils.isEmpty(networkConfig.getIp()) || StringUtils.isEmpty(networkConfig.getNetmask())) {
            logger.warn("LAN静态IP配置缺少必要参数，跳过配置: IP={}, 子网掩码={}",
                    networkConfig.getIp(), networkConfig.getNetmask());
            return;
        }

        // 配置静态IP地址、子网掩码和网关
        StringBuilder commandBuilder = new StringBuilder();
        commandBuilder.append(String.format("nmcli con mod %s connection.autoconnect yes %s %s/%s",
                networkName, IPV4_ADDRESS, networkConfig.getIp(), IpUtil.getMaskNum(networkConfig.getNetmask())));

        // 只有网关不为空时才添加网关配置
        if (StringUtils.isNotEmpty(gateway)) {
            commandBuilder.append(String.format(" %s %s", IPV4_GATEWAY, gateway));
        }

        commandBuilder.append(String.format(" %s manual", IPV4_METHOD));

        String staticCommand = commandBuilder.toString();
        executeNmcliCommand(staticCommand);
        logger.info("配置LAN静态IP: {}", networkName);

        // 配置访问路由
        configureRoute(networkName, networkConfig.getVisit1(), gateway, "访问路由1");
        configureRoute(networkName, networkConfig.getVisit2(), gateway, "访问路由2");
        configureRoute(networkName, networkConfig.getVisit3(), gateway, "访问路由3");
    }

    /**
     * 配置单个路由
     */
    private void configureRoute(String networkName, String destination, String gateway, String routeName)
            throws IOException, InterruptedException {
        if (StringUtils.isNotEmpty(destination) && StringUtils.isNotEmpty(gateway)) {
            String routeCommand = String.format("nmcli con mod %s +%s \"%s %s\"",
                    networkName, IPV4_ROUTES, destination, gateway);
            String[] command = {"/bin/sh", "-c", routeCommand};

            Runtime.getRuntime().exec(command);
            Thread.sleep(COMMAND_WAIT_TIME);
            logger.info("配置{}: {} -> {}", routeName, destination, gateway);
        } else if (StringUtils.isNotEmpty(destination) && StringUtils.isEmpty(gateway)) {
            logger.warn("配置{}失败: 目标地址={}, 网关为空", routeName, destination);
        }
    }

    /**
     * 使用nmcli命令获取指定网卡的详细配置信息
     * <p>
     * 执行流程：
     * 1. 通过nmcli connection show命令获取连接配置信息
     * 2. 解析命令输出获取网络模式、IP、子网掩码、网关、DNS等信息
     * 3. 根据网络模式获取动态信息
     * - DHCP模式：获取实际分配的IP和网关
     * - STATIC模式：获取路由配置信息
     *
     * @param netName 网卡名称
     * @return 网卡配置对象
     * @throws IOException IO异常
     */
    private NetworkConfig getNetworkConfigByNmcliSingle(String netName) throws IOException {
        NetworkConfig networkConfig = new NetworkConfig();
        networkConfig.setName(netName);

        // 1. 获取连接的基本配置信息
        Map<String, String> connectionInfo = getConnectionInfoByNmcli(netName);

        // 2. 设置网络模式
        String method = connectionInfo.get(IPV4_METHOD);
        if ("auto".equals(method)) {
            networkConfig.setMode(MODE_DHCP);
        } else if ("manual".equals(method)) {
            networkConfig.setMode(MODE_STATIC);
        } else {
            networkConfig.setMode(MODE_STATIC); // 默认为静态模式
        }

        // 3. 设置基本网络参数
        parseAddressInfo(connectionInfo.get(IPV4_ADDRESSES), networkConfig);

        // 4. 设置网关
        String gateway = connectionInfo.get(IPV4_GATEWAY);
        if (StringUtils.isNotEmpty(gateway) && !"--".equals(gateway)) {
            networkConfig.setGateway(connectionInfo.get(IPV4_GATEWAY));
        }

        // 5. 设置DNS服务器
        parseDnsInfo(connectionInfo.get(IPV4_DNS), networkConfig);

        // 6. 根据网络模式获取动态信息
        if (MODE_DHCP.equals(networkConfig.getMode())) {
            getIpAddrByNmcli(netName, networkConfig);
            getGatewayByNmcli(networkConfig);
        }
        if (MODE_STATIC.equals(networkConfig.getMode())) {
            getVisitByNmcli(netName, networkConfig);
        }

        return networkConfig;
    }

    /**
     * 解析地址信息
     */
    private void parseAddressInfo(String addresses, NetworkConfig networkConfig) {
        if (StringUtils.isNotEmpty(addresses)) {
            String[] addrParts = addresses.split("/");
            if (addrParts.length >= 2) {
                networkConfig.setIp(addrParts[0]);
                networkConfig.setNetmask(IpUtils.mask2ipMask(addrParts[1]));
            }
        }
    }

    /**
     * 解析DNS信息
     */
    private void parseDnsInfo(String dns, NetworkConfig networkConfig) {
        if (StringUtils.isNotEmpty(dns) && !"--".equals(dns)) {
            String[] dnsServers = dns.split(",");
            if (dnsServers.length >= 1) {
                networkConfig.setDns1(dnsServers[0].trim());
            }
            if (dnsServers.length >= 2) {
                networkConfig.setDns2(dnsServers[1].trim());
            }
        }
    }

    /**
     * 使用nmcli命令获取连接的基本配置信息
     * <p>
     * 通过执行"nmcli connection show <connection-name>"命令获取连接详细配置
     * 解析命令输出，提取网络配置参数
     *
     * @param netName 网卡名称
     * @return 配置信息Map
     * @throws IOException IO异常
     */
    private Map<String, String> getConnectionInfoByNmcli(String netName) throws IOException {
        Map<String, String> configMap = new HashMap<>();

        try {
            String[] command = {"nmcli", "connection", "show", netName, "|", "grep", "ipv4"};
            List<String> cmdOutput = executeCommand(command);

            // 解析命令输出，提取配置信息
            for (String line : cmdOutput) {
                if (StringUtils.isNotEmpty(line) && line.contains(":")) {
                    String[] parts = line.split(":", 2);
                    if (parts.length == 2) {
                        String key = parts[0].trim();
                        String value = parts[1].trim();
                        configMap.put(key, value);
                    }
                }
            }

            return configMap;
        } catch (Exception e) {
            logger.error("获取连接配置信息失败: {}", netName, e);
            throw new IOException("获取连接配置信息失败", e);
        }
    }

    /**
     * 使用nmcli获取网卡的实际IP地址和子网掩码（用于DHCP模式）
     * <p>
     * 通过执行"nmcli device show <device-name>"命令获取设备的实际IP配置信息
     * 解析命令输出，提取IP地址和子网掩码
     *
     * @param netName       网卡名称
     * @param networkConfig 网络配置对象（用于设置获取到的IP信息）
     * @throws IOException IO异常
     */
    public void getIpAddrByNmcli(String netName, NetworkConfig networkConfig) throws IOException {
        try {
            String[] command = {"nmcli", "device", "show", netName};
            List<String> cmdOutput = executeCommand(command);
            // 解析命令输出，查找IP地址信息
            for (String line : cmdOutput) {
                logger.debug("解析设备信息行: {}", line);
                // 查找IP4.ADDRESS行（格式：IP4.ADDRESS[1]: *************/24）
                if (line.contains("IP4.ADDRESS[1]:")) {
                    String[] parts = line.split(":");
                    if (parts.length >= 2) {
                        String addressInfo = parts[1].trim();
                        parseAddressInfo(addressInfo, networkConfig);
                    }
                    break; // 找到第一个IP地址即可
                }
            }
        } catch (Exception e) {
            logger.error("获取网卡IP地址失败: {}", netName, e);
            throw new IOException("获取网卡IP地址失败", e);
        }
    }

    /**
     * 使用nmcli获取默认网关地址（用于DHCP模式）
     * <p>
     * 通过执行"nmcli device show <device-name>"命令获取设备的网关信息
     * 查找IP4.GATEWAY条目，提取网关地址
     *
     * @param networkConfig 网络配置对象（用于设置获取到的网关信息）
     * @throws IOException IO异常
     */
    public void getGatewayByNmcli(NetworkConfig networkConfig) throws IOException {
        try {
            String[] command = {"nmcli", "device", "show", networkConfig.getName()};
            List<String> cmdOutput = executeCommand(command);

            logger.debug("nmcli device show网关命令输出: {}", JSONObject.toJSONString(cmdOutput));

            // 解析命令输出，查找网关信息
            for (String line : cmdOutput) {
                logger.debug("解析网关信息行: {}", line);
                // 查找IP4.GATEWAY行（格式：IP4.GATEWAY: ***********）
                if (line.contains("IP4.GATEWAY:")) {
                    String[] parts = line.split(":");
                    if (parts.length >= 2 && !"--".equals(parts[1].trim())) {
                        String gateway = parts[1].trim();
                        networkConfig.setGateway(gateway);
                    }
                    break;
                }
            }
        } catch (Exception e) {
            logger.error("获取网关信息失败: {}", networkConfig.getName(), e);
            throw new IOException("获取网关信息失败", e);
        }
    }

    /**
     * 使用nmcli获取静态路由配置信息（用于STATIC模式）
     * <p>
     * 通过执行"nmcli connection show <connection-name>"命令获取连接的路由配置
     * 解析ipv4.routes字段，提取访问路由信息
     *
     * @param netName       网卡名称
     * @param networkConfig 网络配置对象（用于设置获取到的路由信息）
     * @throws IOException IO异常
     */
    public void getVisitByNmcli(String netName, NetworkConfig networkConfig) throws IOException {
        try {
            String[] command = {"/bin/sh", "-c", "nmcli connection show " + netName + " | grep " + IPV4_ROUTES};
            List<String> cmdOutput = executeCommand(command);

            // 解析命令输出，查找路由信息
            List<String> visitList = parseRouteInfo(cmdOutput, networkConfig);

            // 设置访问路由（最多3个）
            setVisitRoutes(networkConfig, visitList);

        } catch (Exception e) {
            logger.error("获取路由配置失败: {}", netName, e);
            throw new IOException("获取路由配置失败", e);
        }
    }

    /**
     * 解析路由信息
     * 格式：ipv4.routes: { ip = ***********/24, nh = ********** }; { ip = ***********/24, nh = ********** }
     */
    private List<String> parseRouteInfo(List<String> cmdOutput, NetworkConfig networkConfig) {
        List<String> visitList = new ArrayList<>();

        for (String line : cmdOutput) {
            // 查找ipv4.routes行
            if (line.contains(IPV4_ROUTES + ":")) {
                String[] parts = line.split(":", 2);
                if (parts.length >= 2) {
                    String routesInfo = parts[1].trim();
                    if (StringUtils.isNotEmpty(routesInfo) && !"--".equals(routesInfo)) {
                        // 按分号分割多个路由条目
                        String[] routes = routesInfo.split(";");

                        for (String route : routes) {
                            route = route.trim();
                            if (StringUtils.isNotEmpty(route)) {
                                // 移除大括号
                                route = route.replaceAll("[{}]", "").trim();

                                String destination = null;
                                String gateway = null;
                                // 按逗号分割ip和nh部分
                                String[] split = route.split(",");
                                for (String part : split) {
                                    part = part.trim();
                                    if (part.startsWith("ip") && part.contains("=")) {
                                        // 提取ip值：ip = ***********/24
                                        String[] ipParts = part.split("=", 2);
                                        if (ipParts.length >= 2) {
                                            destination = ipParts[1].trim();
                                        }
                                    } else if (part.startsWith("nh") && part.contains("=")) {
                                        // 提取nh值：nh = **********
                                        String[] nhParts = part.split("=", 2);
                                        if (nhParts.length >= 2) {
                                            gateway = nhParts[1].trim();
                                        }
                                    }
                                }

                                // 添加到访问列表
                                if (StringUtils.isNotEmpty(destination)) {
                                    visitList.add(destination);
                                    logger.debug("解析到路由: {} -> {}", destination, gateway);

                                    // 设置网关（如果还没有设置的话）
                                    if (StringUtils.isNotEmpty(gateway) && StringUtils.isEmpty(networkConfig.getGateway())) {
                                        networkConfig.setGateway(gateway);
                                    }
                                }
                            }
                        }
                    }
                }
                break;
            }
        }

        return visitList;
    }

    /**
     * 设置访问路由
     */
    private void setVisitRoutes(NetworkConfig networkConfig, List<String> visitList) {
        if (visitList.size() > 0) {
            networkConfig.setVisit1(visitList.get(0));
        }
        if (visitList.size() > 1) {
            networkConfig.setVisit2(visitList.get(1));
        }
        if (visitList.size() > 2) {
            networkConfig.setVisit3(visitList.get(2));
        }
    }

    /**
     * 执行命令并获取输出结果
     */
    private List<String> executeCommand(String[] command) throws IOException {
        List<String> result = new ArrayList<>();

        try {
            Process process = Runtime.getRuntime().exec(command);
            try (InputStream inputStream = process.getInputStream();
                 InputStreamReader reader = new InputStreamReader(inputStream);
                 BufferedReader bufferedReader = new BufferedReader(reader)) {

                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    result.add(line);
                }

                process.waitFor();
                return result;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("命令执行被中断", e);
        }
    }

    /**
     * 执行nmcli命令（无需返回结果）
     */
    private void executeNmcliCommand(String command) throws IOException, InterruptedException {
        logger.debug("执行nmcli命令: {}", command);
        Runtime.getRuntime().exec(command);
        Thread.sleep(COMMAND_WAIT_TIME);
    }

    /**
     * 检查IP地址是否与现有网卡配置存在同一网段冲突
     * <p>
     * 校验逻辑：
     * 1. 获取系统中所有已配置的网卡信息
     * 2. 遍历每个网卡的IP配置
     * 3. 检查新配置的IP是否与现有IP在同一网段
     * 4. 排除当前正在配置的网卡（允许修改自己的IP）
     *
     * @param newConfig 新的网络配置
     * @throws Exception 如果存在同一网段冲突则抛出异常
     */
    public void checkSameSubnetConflict(NetworkConfig newConfig) throws Exception {
        // 只对静态IP模式进行校验
        if (!MODE_STATIC.equals(newConfig.getMode()) ||
            StringUtils.isEmpty(newConfig.getIp()) ||
            StringUtils.isEmpty(newConfig.getNetmask())) {
            return;
        }

        try {
            // 获取系统中所有网卡配置
            List<NetworkConfig> existingConfigs = getNetConfigByNmcli();

            String conflictNetworkName = IpUtil.checkSameSubnetConflict(
                    newConfig.getIp(),
                    newConfig.getNetmask(),
                    newConfig.getName(),
                    existingConfigs);

            if (conflictNetworkName != null) {
                String errorMsg = String.format("IP地址 %s 与网卡 %s 在同一网段，不允许配置",
                        newConfig.getIp(), conflictNetworkName);
                logger.warn(errorMsg);
                throw new Exception(errorMsg);
            }

        } catch (IOException e) {
            logger.error("获取网卡配置失败，跳过同一网段校验");
        } catch (Exception e) {
            if (e.getMessage().contains("在同一网段")) {
                throw e;
            }
            logger.error("同一网段校验过程中发生异常");
        }
    }

}