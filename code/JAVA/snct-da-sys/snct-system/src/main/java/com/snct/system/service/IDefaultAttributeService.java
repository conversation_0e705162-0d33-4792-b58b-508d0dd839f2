package com.snct.system.service;

import java.util.List;
import com.snct.system.domain.DefaultAttribute;

/**
 * 设备默认属性Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface IDefaultAttributeService 
{
    /**
     * 查询设备默认属性
     * 
     * @param id 设备默认属性主键
     * @return 设备默认属性
     */
    public DefaultAttribute selectDefaultAttributeById(Long id);

    /**
     * 查询设备默认属性列表
     * 
     * @param defaultAttribute 设备默认属性
     * @return 设备默认属性集合
     */
    public List<DefaultAttribute> selectDefaultAttributeList(DefaultAttribute defaultAttribute);

    /**
     * 新增设备默认属性
     * 
     * @param defaultAttribute 设备默认属性
     * @return 结果
     */
    public int insertDefaultAttribute(DefaultAttribute defaultAttribute);

    /**
     * 修改设备默认属性
     * 
     * @param defaultAttribute 设备默认属性
     * @return 结果
     */
    public int updateDefaultAttribute(DefaultAttribute defaultAttribute);

    /**
     * 批量删除设备默认属性
     * 
     * @param ids 需要删除的设备默认属性主键集合
     * @return 结果
     */
    public int deleteDefaultAttributeByIds(Long[] ids);

    /**
     * 删除设备默认属性信息
     * 
     * @param id 设备默认属性主键
     * @return 结果
     */
    public int deleteDefaultAttributeById(Long id);

    public List<String> selectDefaultAttributeStrs(Integer type);
}
