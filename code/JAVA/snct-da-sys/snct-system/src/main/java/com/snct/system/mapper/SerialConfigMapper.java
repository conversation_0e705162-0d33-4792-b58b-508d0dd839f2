package com.snct.system.mapper;

import java.util.List;
import com.snct.system.domain.SerialConfig;

/**
 * 串口配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface SerialConfigMapper 
{
    /**
     * 查询串口配置
     * 
     * @param id 串口配置主键
     * @return 串口配置
     */
    public SerialConfig selectSerialConfigById(Long id);

    /**
     * 查询串口配置列表
     * 
     * @param serialConfig 串口配置
     * @return 串口配置集合
     */
    public List<SerialConfig> selectSerialConfigList(SerialConfig serialConfig);

    /**
     * 新增串口配置
     * 
     * @param serialConfig 串口配置
     * @return 结果
     */
    public int insertSerialConfig(SerialConfig serialConfig);

    /**
     * 修改串口配置
     * 
     * @param serialConfig 串口配置
     * @return 结果
     */
    public int updateSerialConfig(SerialConfig serialConfig);

    /**
     * 删除串口配置
     * 
     * @param id 串口配置主键
     * @return 结果
     */
    public int deleteSerialConfigById(Long id);

    /**
     * 批量删除串口配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSerialConfigByIds(Long[] ids);


    /**
     * 查询串口配置单个对象
     * @param serialConfig
     * @return
     */
    public SerialConfig selectSerialConfig(SerialConfig serialConfig);

}
