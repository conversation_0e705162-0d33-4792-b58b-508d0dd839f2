package com.snct.system.domain;

import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 设备对象 bu_device
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public class Device extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 设备ID */
    private Long id;

    /** 船sn */
    @Excel(name = "船sn")
    private String sn;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String name;

    /** 设备类型 */
    @Excel(name = "设备类型")
    private Integer type;

    /** 设备编号 */
    @Excel(name = "设备编号")
    private String code;

    /** 优先级 */
    @Excel(name = "优先级")
    private Long cost;

    /** 启用状态 */
    @Excel(name = "启用状态")
    private Integer enable;

    /** 机器MAC地址 */
    @Excel(name = "机器MAC地址")
    private String mac;

    /** 传输状态 */
    @Excel(name = "传输状态")
    private Long transferStatus;

    /** 传输间隔(单位：秒) */
    @Excel(name = "传输间隔(单位：秒)")
    private Long compartment;

    /** 连接方式 */
    @Excel(name = "连接方式")
    private Integer connectType;

    /** 波特率 */
    @Excel(name = "波特率")
    private Integer baudRate;

    /** 数据位 */
    @Excel(name = "数据位")
    private Integer dataBits;

    /** 停止位 */
    @Excel(name = "停止位")
    private Integer stopBits;

    /** 校验位 */
    @Excel(name = "校验位")
    private Integer parity;

    /** 串口号 */
    @Excel(name = "串口号")
    private String serialPort;

    /** IP地址 */
    @Excel(name = "IP地址")
    private String ip;

    /** 端口 */
    @Excel(name = "端口")
    private Integer port;

    /** 设备连接状态 */
    @Excel(name = "设备连接状态")
    private Long connectStatus;

    /** 消息长度限制 */
    @Excel(name = "消息长度限制")
    private Integer msgLength;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setSn(String sn) 
    {
        this.sn = sn;
    }

    public String getSn() 
    {
        return sn;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setType(Integer type)
    {
        this.type = type;
    }

    public Integer getType()
    {
        return type;
    }

    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }

    public void setCost(Long cost) 
    {
        this.cost = cost;
    }

    public Long getCost() 
    {
        return cost;
    }

    public void setEnable(Integer enable)
    {
        this.enable = enable;
    }

    public Integer getEnable()
    {
        return enable;
    }

    public void setMac(String mac) 
    {
        this.mac = mac;
    }

    public String getMac() 
    {
        return mac;
    }

    public void setTransferStatus(Long transferStatus) 
    {
        this.transferStatus = transferStatus;
    }

    public Long getTransferStatus() 
    {
        return transferStatus;
    }

    public void setCompartment(Long compartment) 
    {
        this.compartment = compartment;
    }

    public Long getCompartment() 
    {
        return compartment;
    }

    public void setConnectType(Integer connectType)
    {
        this.connectType = connectType;
    }

    public Integer getConnectType()
    {
        return connectType;
    }

    public void setBaudRate(Integer baudRate)
    {
        this.baudRate = baudRate;
    }

    public Integer getBaudRate()
    {
        return baudRate;
    }

    public void setDataBits(Integer dataBits)
    {
        this.dataBits = dataBits;
    }

    public Integer getDataBits()
    {
        return dataBits;
    }

    public void setStopBits(Integer stopBits)
    {
        this.stopBits = stopBits;
    }

    public Integer getStopBits()
    {
        return stopBits;
    }

    public void setParity(Integer parity)
    {
        this.parity = parity;
    }

    public Integer getParity()
    {
        return parity;
    }

    public void setSerialPort(String serialPort) 
    {
        this.serialPort = serialPort;
    }

    public String getSerialPort() 
    {
        return serialPort;
    }

    public void setIp(String ip)
    {
        this.ip = ip;
    }

    public String getIp()
    {
        return ip;
    }

    public void setPort(Integer port)
    {
        this.port = port;
    }

    public Integer getPort()
    {
        return port;
    }

    public void setConnectStatus(Long connectStatus) 
    {
        this.connectStatus = connectStatus;
    }

    public Long getConnectStatus()
    {
        return connectStatus;
    }

    public void setMsgLength(Integer msgLength)
    {
        this.msgLength = msgLength;
    }

    public Integer getMsgLength()
    {
        return msgLength;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sn", getSn())
            .append("name", getName())
            .append("type", getType())
            .append("code", getCode())
            .append("cost", getCost())
            .append("enable", getEnable())
            .append("mac", getMac())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("transferStatus", getTransferStatus())
            .append("compartment", getCompartment())
            .append("connectType", getConnectType())
            .append("baudRate", getBaudRate())
            .append("dataBits", getDataBits())
            .append("stopBits", getStopBits())
            .append("parity", getParity())
            .append("serialPort", getSerialPort())
            .append("ip", getIp())
            .append("port", getPort())
            .append("connectStatus", getConnectStatus())
            .append("msgLength", getMsgLength())
            .toString();
    }
}
