package com.snct.common.constant;

/**
 * 缓存的key 常量
 * 
 * <AUTHOR>
 */
public class CacheConstants
{
    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 设备登录token redis key
     */
    public static final String DEVICE_TOKEN_KEY = "device_tokens:";

    /**
     * 设备数据 redis key
     */
    public static final String DEVICE_DATA_KEY = "device_data:";

    /**
     * 设备历史数据 redis key
     */
    public static final String DEVICE_HISTORY_DATA_KEY = "device_history_data:";

    /**
     * 北斗发送数据时间 redis key
     */
    public static final String BD_SEND_TIME = "bd_send_time:";

    /**
     * 串口连接状态 redis key
     */
    public static final String CK_CONNECT_STATUS = "ck_connect_status:";
}
